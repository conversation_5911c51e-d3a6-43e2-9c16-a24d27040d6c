{"name": "Crystal Clear Backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon dist/index.js", "dev": "nodemon app/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.2.0", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/md5": "^2.3.5", "@types/morgan": "^1.9.9", "@types/node": "^20.12.11", "@types/nodemailer": "^6.4.14", "@types/puppeteer": "^7.0.4", "@types/uuid": "^9.0.8", "cors": "^2.8.5", "eslint": "^8.57.0", "globals": "^15.1.0", "prisma": "^5.13.0", "ts-node": "^10.9.2", "typescript": "^5.4.5", "typescript-eslint": "^7.8.0"}, "dependencies": {"@aws-sdk/client-lambda": "^3.583.0", "@aws-sdk/client-sqs": "^3.614.0", "@prisma/client": "^5.10.2", "axios": "^1.6.7", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "colors": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "moment": "^2.30.1", "morgan": "^1.10.0", "nodemailer": "^6.9.10", "nodemon": "^3.0.3", "puppeteer": "^21.9.0", "socket.io": "^4.7.5", "stripe": "^16.5.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0"}}