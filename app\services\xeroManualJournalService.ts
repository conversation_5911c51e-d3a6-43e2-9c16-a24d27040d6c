import axios from "axios";
import moment from "moment";
import { prisma } from "../client/prisma";
import { EntityTypes } from "../utils/constants";

export async function createJournal(
    stripeFeesAmount: number,
    token: string,
    tenantId: string
) {

    const products = await prisma.paymentGateway.findFirst({
        where: {
            name: 'Stripe'
        }
    })

    const now = moment();

    const data = {
        Date: now.format('YYYY-MM-DD'),
        Narration: 'Stripe Fees',
        Status: 'POSTED',
        JournalLines: [
            {
                LineAmount: -stripeFeesAmount,
                AccountCode: products?.coaCode,
                Description: "Stripe fees",

            },
            {
                LineAmount: stripeFeesAmount,
                AccountCode: products?.feesCode,

            },
        ],
    };

    console.log('data: ', data);


    const create = await createJournalRequest(data, token, tenantId);
    console.log('create: ', create);
    if (create) {
        await prisma.stripeJournalEntry.create({
            data: {
                stripeAmount: stripeFeesAmount.toString(),
                journalDate: new Date().toISOString(),
                journalMonth: now.format('MMM_YYYY'),
                journalNo: `Stripe_${moment().format('DD_MMM_YYYY')}`,
                xeroJournalId: create,
            },
        });
        console.log('moment().format(YYYY_MM_DD): ', moment().format('YYYY_MM_DD'));

        let checkExistingPayment = await prisma.customerSynclog.findFirst({
            where: {
                createdOrderDate: moment().format('YYYY-MM-DD'),
                paymentGateway: 'stripe'
            },
        });

        console.log('checkExistingPayment', checkExistingPayment);
        console.log('!checkExistingPayment', !checkExistingPayment);
        if (!checkExistingPayment) {

            const connection = await prisma.connection.findFirst({});
            await prisma.customerSynclog.create({
                data: {
                    type: EntityTypes.SHOPIFY_ORDER,
                    connectionId: String(connection?.id),
                    syncHistory: [],
                    totalDiscount: '0',
                    subTotalPrice: '0',
                    totalTax: '0',
                    paymentGateway: 'stripe',
                    gatewayTransaction: stripeFeesAmount.toString(),
                    createdOrderDate: moment().format('YYYY-MM-DD'),
                }
            });

        }
        else {
            const data = await prisma.customerSynclog.updateMany({
                where: {
                    createdOrderDate: moment().format('YYYY-MM-DD'),
                    paymentGateway: 'stripe'
                },
                data: {
                    gatewayTransaction: stripeFeesAmount.toString()
                }
            })
            console.log('data customerSynclog: ', data);
        }


    }
}

export async function updateJournal(
    journalId: string,
    stripeFeesAmount: number,
    token: string,
    tenantId: string
) {
    const products = await prisma.paymentGateway.findFirst({
        where: {
            name: 'Stripe'
        }
    })
    const now = moment();

    const data = {
        Date: now.format('YYYY-MM-DD'),
        Narration: 'Stripe Fees',
        Status: 'POSTED',
        JournalLines: [
            {
                LineAmount: -stripeFeesAmount,
                AccountCode: products?.coaCode,
                Description: "Stripe fees",

            },
            {
                LineAmount: stripeFeesAmount,
                AccountCode: products?.feesCode,

            },
        ],
    };

    const update = await updateJournalRequest(journalId, data, token, tenantId);

    if (update) {
        await prisma.stripeJournalEntry.updateMany({
            where: {
                xeroJournalId: journalId,
            },
            data: {
                stripeAmount: stripeFeesAmount.toString(),
            },
        });

        console.log('${moment().format(YYYY_MM_DD)}: ', moment().format('YYYY-MM-DD'));

        const data = await prisma.customerSynclog.updateMany({
            where: {
                createdOrderDate: moment().format('YYYY-MM-DD'),
                paymentGateway: 'stripe'
            },
            data: {
                gatewayTransaction: stripeFeesAmount.toString()
            }
        })
        console.log('data customerSynclog update: ', data);
    }
}

export async function createJournalRequest(
    journalData: any,
    token: string,
    tenantId: string
) {
    try {
        const journalResponse = await axios.put(
            'https://api.xero.com/api.xro/2.0/ManualJournals',
            {
                ManualJournals: [journalData],
            },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Xero-Tenant-Id': `${tenantId}`,
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
            }
        );

        if (!journalResponse?.data?.ManualJournals?.length) {
            return null;
        }

        return journalResponse.data.ManualJournals[0].ManualJournalID;
    } catch (error) {
        console.log(error);
    }
    return null;
}

export async function updateJournalRequest(
    journalId: string,
    journalData: any,
    token: string,
    tenantId: string
) {
    try {
        const journalResponse = await axios.post(
            `https://api.xero.com/api.xro/2.0/ManualJournals/${journalId}`,
            {
                ManualJournals: [journalData],
            },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Xero-Tenant-Id': `${tenantId}`,
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
            }
        );

        if (!journalResponse?.data?.ManualJournals?.length) {
            return null;
        }

        return journalResponse.data.ManualJournals[0].ManualJournalID;
    } catch (error) {
        console.log(error);
    }

    return null;
}

export async function xeroRefreshToken(xeroData: any) {
    try {
        const clientId = process.env.XERO_CLIENT_ID;
        const clientSecret = process.env.XERO_CLIENT_SECRET;
        const refreshToken = xeroData.xeroCredentials.refresh_token;

        const authHeader =
            'Basic ' +
            Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
        const params = new URLSearchParams();
        params.append('grant_type', 'refresh_token');
        params.append('refresh_token', refreshToken);

        const refreshTokenResponse = await axios.post(
            'https://identity.xero.com/connect/token',
            params,
            {
                headers: {
                    Authorization: authHeader,
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            }
        );

        const xeroTokenDetails = {
            ...xeroData.xeroCredentials,
            access_token: refreshTokenResponse?.data?.access_token,
            refresh_token: refreshTokenResponse?.data?.refresh_token,
            id_token: refreshTokenResponse?.data?.id_token,
            expires_in: Math.floor(Date.now() / 1000) + 5 * 300,
        };

        await prisma.connection.update({
            where: {
                id: xeroData.id,
            },
            data: {
                xeroCredentials: xeroTokenDetails,
            },
        });

        return xeroTokenDetails;
    } catch (error: any) {
        console.error('Error refreshing token: ', error);
        throw error;
    }
}

