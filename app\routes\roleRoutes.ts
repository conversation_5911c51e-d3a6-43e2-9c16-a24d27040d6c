import express from 'express';
import asyncHandler from '../utils/async-handler';
import { roleService } from '../services/roleService';
import { isAdmin } from '../middlewares/authMiddleware';
import { RequestExtended } from '../interfaces/global';

const router = express.Router();

router.get(
    '/',
    isAdmin,
    asyncHandler(async () => {
        return roleService.getAllRoles();
    })
);

router.put(
    '/:id/status/:status',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        return roleService.changeRoleStatus(req.params.id, req.params.status, req.user);
    })
);

export default router;
