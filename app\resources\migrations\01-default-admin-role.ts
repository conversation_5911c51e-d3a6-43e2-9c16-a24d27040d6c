import { prisma } from '../../client/prisma';
import { hashPassword } from '../../helpers/passwordHelper';
import { RoleTypes } from '../../utils/constants';

export async function up() {

	const roleData = {
		[RoleTypes.SUPER_ADMIN]: {
			name: 'Super Admin',
			description: 'All Permissions granted',
		},
		[RoleTypes.SALES_REP]: {
			name: 'Sales Rep',
			description: 'Able to access the Dashboard, Synclogs and Sales reports',
		},
		[RoleTypes.INVENTORY_MANAGER]: {
			name: 'Inventory Manager',
			description: 'Able to access the Synclogs',
		},
		[RoleTypes.ACCOUNT_HEADS]: {
			name: 'Account Heads',
			description: 'Able to access the Dashboard, Synclogs, Sales reports and Configuration',
		}
	}

	for(const role in roleData) {
		const data = roleData[role as keyof typeof roleData]

		const createRole = await prisma.role.create({
			data: {
				name: data.name,
				description: data.description,
				type: role,
			}
		})

		if(role === RoleTypes.SUPER_ADMIN) {
			await prisma.users.create({
				data: {
					name: 'Super Admin',
					password: await hashPassword(`${process.env.ADMIN_PASSWORD || 'superAdmin@123'}`),
					email: `${process.env.ADMIN_EMAIL || '<EMAIL>'}`,
					roleId: createRole.id,
				},
			});
		}
	}

}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function down() {
	// do nothing
}
