import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { awsConfig } from './aws';

const sqs = new SQSClient(awsConfig);

export async function sendMessageToXeroProductQueue(
    data: any,
) {
    const params = new SendMessageCommand({
        MessageBody: JSON.stringify({ ...data }), // Customize the message content
        QueueUrl: `${process.env.XERO_PRODUCT_QUEUE}`, // Replace with your SQS Queue URL
    });

    try {
        await sqs.send(params);
    } catch (error) {
        console.log(error);
    }
}
