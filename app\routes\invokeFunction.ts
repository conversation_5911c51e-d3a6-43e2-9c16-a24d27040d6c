import express, { Request } from 'express';
import { isAdmin } from '../middlewares/authMiddleware';
import { invokeFunctionService } from '../services/invokeFunctionService';
import asyncHandler from '../utils/async-handler';

const router = express.Router();

router.get(
	'/woocommerce/order',
	isAdmin,
	asyncHandler(async () => {
		return invokeFunctionService.invokeWoocommerceOrder();
	})
);

router.get(
	'/woocommerce/salesOrders',
	isAdmin,
	asyncHandler(async (req: Request) => {
		return await invokeFunctionService.invokeWoocommerceSalesOrder(req);
	})
);

router.get(
	'/hubspot/invoice',
	isAdmin,
	asyncHandler(async () => {
		return invokeFunctionService.invokeHubspotInvoices();
	})
);

router.get(
	'/hubspot/payment',
	isAdmin,
	asyncHandler(async () => {
		return invokeFunctionService.invokeHubspotPayment();
	})
);

router.get(
	'/journals',
	isAdmin,
	asyncHandler(async () => {
		return invokeFunctionService.invokeJournal();
	})
);

router.get(
	'/linnworks/orderTracking',
	isAdmin,
	asyncHandler(async () => {
		return invokeFunctionService.invokeLinnworksOrderTracking();
	})
);

router.get(
	'/linnworks/stock',
	isAdmin,
	asyncHandler(async () => {
		return invokeFunctionService.invokeSyncStock();
	})
);
router.get(
	'/shopify/orders',
	isAdmin,
	asyncHandler(async () => {
		return invokeFunctionService.invokeShopifyGetOrders();
	})
);
router.get(
	'/shopify/products',
	isAdmin,
	asyncHandler(async () => {
		return invokeFunctionService.invokeShopifyGetProducts();
	})
);

router.get(
	'/xero/accounts',
	isAdmin,
	asyncHandler(async () => {
		return invokeFunctionService.invokeXeroAccounts();
	})
);

export default router;
