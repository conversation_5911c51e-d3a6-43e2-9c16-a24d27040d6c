-- CreateTable
CREATE TABLE "shopifyOrder" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "currency" TEXT NOT NULL,
    "pricesIncludeTax" BOOLEAN NOT NULL,
    "discountTotal" DOUBLE PRECISION NOT NULL,
    "shippingTotal" DOUBLE PRECISION NOT NULL,
    "total" DOUBLE PRECISION NOT NULL,
    "totalTax" DOUBLE PRECISION NOT NULL,
    "type" TEXT,
    "connectionId" TEXT NOT NULL,
    "customerId" TEXT NOT NULL,
    "paymentMethod" TEXT NOT NULL,
    "paymentMethodTitle" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "customerIpAddress" TEXT NOT NULL,
    "dateCompleted" TIMESTAMP(3) NOT NULL,
    "datePaidGmt" TIMESTAMP(3) NOT NULL,
    "createdOrderDate" TIMESTAMP(3) NOT NULL,
    "modifiedOrderDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "shopifyOrder_pkey" PRIMARY KEY ("id")
);
