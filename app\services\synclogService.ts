import moment from 'moment';
import { prisma } from '../client/prisma';
import { IQuery } from '../interfaces/global';
import { getSkipCount, hasText } from '../utils/utils';
import ApiException from '../utils/errorHandler';
import { ErrorCodes } from '../utils/response';

// Helper function to calculate search relevance score
function calculateSearchRelevance(invoice: any, searchText: string): number {
	let score = 0;
	const searchLower = searchText.toLowerCase();

	// Get searchable text fields (case-insensitive)
	const invoiceNumber = (invoice.hubSpotInvoiceNumber || '').toLowerCase();
	const firstName = (
		invoice.wooCommerceCustomer?.first_name || ''
	).toLowerCase();
	const lastName = (invoice.wooCommerceCustomer?.last_name || '').toLowerCase();

	// Additional customer fields
	const billingFirstName = (
		invoice.wooCommerceCustomer?.billing?.first_name || ''
	).toLowerCase();
	const billingLastName = (
		invoice.wooCommerceCustomer?.billing?.last_name || ''
	).toLowerCase();
	const shippingFirstName = (
		invoice.wooCommerceCustomer?.shipping?.first_name || ''
	).toLowerCase();
	const shippingLastName = (
		invoice.wooCommerceCustomer?.shipping?.last_name || ''
	).toLowerCase();

	// Collect all searchable fields
	const searchableFields = [
		invoiceNumber,
		firstName,
		lastName,
		billingFirstName,
		billingLastName,
		shippingFirstName,
		shippingLastName,
	].filter((field) => field.length > 0);

	// Scoring system (higher scores = more relevant):

	searchableFields.forEach((field) => {
		// 1. Exact matches get highest score
		if (field === searchLower) score += 1000;

		// 2. Starts with search text gets high score
		if (field.startsWith(searchLower)) score += 500;

		// 3. Contains search text gets medium score
		if (field.includes(searchLower)) score += 100;

		// 4. Bonus points for shorter fields (more precise matches)
		if (field.includes(searchLower)) {
			score += Math.max(0, 50 - field.length);
		}
	});

	// 5. Bonus for multiple word matches
	const searchWords = searchText.toLowerCase().split(/\s+/);
	if (searchWords.length > 1) {
		searchWords.forEach((word) => {
			searchableFields.forEach((field) => {
				if (field.includes(word)) score += 25;
			});
		});
	}

	return score;
}

async function getSynclogs(query: IQuery) {
	const _query: any = {};

	const sort: any[] = [];

	if (query.sortBy && query.sortOrder) {
		sort.push({
			[query.sortBy]: query.sortOrder,
		});
	}

	sort.push(
		{
			createdAt: 'desc',
		},
		{
			id: 'desc',
		}
	);

	const orQuey: any[] = [];

	if (hasText(query.text)) {
		orQuey.push({
			wooCommerceOrderId: {
				contains: query.text,
				mode: 'insensitive',
			},
		});
	}

	if (hasText(query.fromDate) && hasText(query.toDate)) {
		orQuey.push({
			createdAt: {
				gte: moment(query.fromDate).startOf('day').toISOString(),
				lt: moment(query.toDate).endOf('day').toISOString(),
			},
		});
	}

	if (hasText(query.invoiceStatus)) {
		if (query.invoiceStatus != 'all') {
			_query.invoiceStatus = query.invoiceStatus;
		}
	}

	// We'll do all search filtering in memory for true case-insensitive matching

	if (orQuey.length) {
		_query.OR = orQuey;
	}

	// If search text is provided, we need to fetch more records and sort by relevance
	let invoices;
	let count;

	if (hasText(query.searchText)) {
		// For case-insensitive search, fetch all records and filter in memory
		// This ensures true case-insensitive matching for JSON fields

		// Fetch all records (with a reasonable limit)
		const allInvoices = await prisma.invoices.findMany({
			where: _query,
			orderBy: sort,
			skip: 0,
			take: 5000, // Increased limit to ensure we get all potential matches
			include: {
				syncLog: true,
				InvoiceLines: true,
			},
		});

		// Apply case-insensitive filtering for all searchable fields
		const searchTextLower = query.searchText.toLowerCase().trim();
		const filteredInvoices = allInvoices.filter((invoice) => {
			// Check hubSpotInvoiceNumber first
			if (
				invoice.hubSpotInvoiceNumber &&
				invoice.hubSpotInvoiceNumber.toLowerCase().includes(searchTextLower)
			) {
				return true;
			}

			// Check customer fields
			const customer = invoice.wooCommerceCustomer as any;
			if (!customer) return false; // No customer data to search

			const searchableFields = [
				customer.first_name,
				customer.last_name,
				customer.billing?.first_name,
				customer.billing?.last_name,
				customer.shipping?.first_name,
				customer.shipping?.last_name,
			].filter((field) => field && typeof field === 'string');

			// Check if any field contains the search text (case-insensitive)
			const matchesField = searchableFields.some((field) =>
				field.toLowerCase().includes(searchTextLower)
			);

			// Also check for individual words if search contains multiple words
			if (!matchesField && searchTextLower.includes(' ')) {
				const searchWords = searchTextLower
					.split(/\s+/)
					.filter((word: any) => word.length > 0);
				return searchWords.some(
					(word: any) =>
						searchableFields.some((field) =>
							field.toLowerCase().includes(word)
						) ||
						(invoice.hubSpotInvoiceNumber &&
							invoice.hubSpotInvoiceNumber.toLowerCase().includes(word))
				);
			}

			return matchesField;
		});

		// Sort by search relevance
		const sortedInvoices = filteredInvoices.sort((a, b) => {
			// Calculate relevance scores
			const scoreA = calculateSearchRelevance(a, query.searchText);
			const scoreB = calculateSearchRelevance(b, query.searchText);

			// Higher score = more relevant = should come first
			if (scoreB !== scoreA) {
				return scoreB - scoreA;
			}

			// If relevance is equal, fall back to creation date
			const dateA = new Date(a.createdAt).getTime();
			const dateB = new Date(b.createdAt).getTime();
			return dateB - dateA;
		});

		// Set count from filtered results
		count = sortedInvoices.length;

		// Apply pagination to the sorted results
		const startIndex = getSkipCount(query.pageNo, query.pageSize);
		const endIndex = startIndex + (Number(query.pageSize) || 10);
		invoices = sortedInvoices.slice(startIndex, endIndex);
	} else {
		// No search text, use regular sorting
		invoices = await prisma.invoices.findMany({
			where: _query,
			orderBy: sort,
			skip: getSkipCount(query.pageNo, query.pageSize),
			take: Number(query.pageSize) || 10,
			include: {
				syncLog: true,
				InvoiceLines: true,
			},
		});
	}

	// For non-search queries, calculate count
	if (!hasText(query.searchText)) {
		count = await prisma.invoices.count({
			where: _query,
		});
	}

	const content = invoices.map((e: any) => {
		return {
			synclogId: e.synclogId ?? e.syncLog?.id ?? null,
			createdAt: e.syncLog?.createdAt ?? e.createdAt,
			updatedAt: e.syncLog?.updatedAt ?? e.updatedAt,
			customer: e.wooCommerceCustomer
				? `${e.wooCommerceCustomer.first_name ?? ''} ${e.wooCommerceCustomer.last_name ?? ''}`.trim() || ''
				: '',
			amount: e.wooCommerceAmount ?? '0',
			invoice: e.hubSpotPaymentStatus ? e.hubSpotPaymentStatus : null,
			status: e.invoiceStatus ? e.invoiceStatus : null,
			wooCommerceOrderId: e.wooCommerceOrderId ?? null,
			hubSpotDealId: e.hubSpotDealId ?? null,
			hubSpotContactId: e.hubSpotContactId ?? null,
			hubSpotQuoteId: e.hubSpotQuoteId ?? null,
			hubSpotInvoiceId: e.hubSpotInvoiceId ?? null,
			hubSpotInvoiceNumber: e.hubSpotInvoiceNumber ?? null,
			xeroContactId: e.xeroContactId ?? null,
			xeroInvoiceId: e.xeroInvoiceId ?? null,
			xeroInvoiceNumber: e.xeroInvoiceNumber ?? null,
			xeroPaymentId: e.xeroPaymentId ?? null,
			postalTrackingNumber: e.postalTrackingNumber ?? null,
			trackingUrl: e.trackingUrl ?? null,
			linnworksOrderNum: e.linnworksOrderNum?.toString() ?? null,
			hubspotDealUrl: e.hubSpotDealId
				? `${process.env.HUBSPOT_DEAL_URL}/${e.hubSpotDealId}`
				: null,
			hubspotContactUrl: e.hubSpotContactId
				? `${process.env.HUBSPOT_CONTACT_URL}/${e.hubSpotContactId}`
				: null,
			hubspotQuoteUrl: e.hubSpotQuoteId
				? `${process.env.HUBSPOT_QUOTE_URL}/${e.hubSpotQuoteId}`
				: null,
			hubspotInvoiceUrl: process.env.HUBSPOT_INVOICE_URL,
			xeroContactUrl: e.xeroContactId
				? `${process.env.XERO_CONTACT_URL}/${e.xeroContactId}`
				: null,
			xeroInvoiceUrl: e.xeroInvoiceId
				? `${process.env.XERO_INVOICE_URL}=${e.xeroInvoiceId}`
				: null,
			xeroPaymentUrl: e.xeroPaymentId
				? `${process.env.XERO_PAYMENT_URL}=${e.xeroPaymentId}`
				: null,
			linnworksOrderUrl: e.linnworksOrderNum
				? `${process.env.LINNWORKS_ORDER_URL}${e.linnworksOrderNum}`
				: null,
		};
	});
	return {
		content,
		count,
	};
}

async function getSyncLogHistoryById(synclogId: string) {
	// Check if the synclogId is provided
	if (!synclogId) {
		throw new ApiException(ErrorCodes.ID_REQUIRED);
	}

	try {
		// Check if the SyncLog exists
		const log = await prisma.synclog.findUnique({
			where: { id: synclogId },
			select: {
				id: true,
				syncHistory: true,
				createdAt: true,
				updatedAt: true,
			},
		});
		if (!log) {
			throw new ApiException(ErrorCodes.INTERNAL_SERVER_ERROR);
		}
		return {
			synclogId: log.id,
			createdAt: log.createdAt,
			updatedAt: log.updatedAt,
			history: log.syncHistory ?? [],
		};
	} catch (error) {
		// Handle unexpected errors
		console.error(error);
		throw new ApiException(ErrorCodes.INTERNAL_SERVER_ERROR);
	}
}

async function getB2CSyncLogHistoryById(synclogId: string) {
	// Check if the synclogId is provided
	if (!synclogId) {
		throw new ApiException(ErrorCodes.ID_REQUIRED);
	}

	try {
		// Check if the SyncLog exists
		const log = await prisma.customerSynclog.findUnique({
			where: { id: synclogId },
			select: {
				id: true,
				syncHistory: true,
				createdAt: true,
				updatedAt: true,
			},
		});
		if (!log) {
			throw new ApiException(ErrorCodes.INTERNAL_SERVER_ERROR);
		}
		return {
			synclogId: log.id,
			createdAt: log.createdAt,
			updatedAt: log.updatedAt,
			history: log.syncHistory ?? [],
		};
	} catch (error) {
		// Handle unexpected errors
		console.error(error);
		throw new ApiException(ErrorCodes.INTERNAL_SERVER_ERROR);
	}
}

async function getCOGSSynclogs(query: IQuery) {
	const cogs = await prisma.cOGSJournalEntry.findMany({
		skip: getSkipCount(query.pageNo, query.pageSize),
		take: Number(query.pageSize) || 10,
	});

	const count = await prisma.cOGSJournalEntry.count({});
	const _data = cogs.map((item) => {
		return {
			...item,
			cogsNumberUrl: `${process.env.COGS_JOURNAL_URL}${item.xeroJournalId}`,
		};
	});
	return {
		content: _data,
		count,
	};
}

async function getB2CSynclogs(query: IQuery) {
	let whereClause: any = {};
	const sort: any[] = [];

	if (query.sortBy && query.sortingOrder) {
		const sortOrder =
			query.sortingOrder.toLowerCase() === 'asc' ? 'asc' : 'desc';
		sort.push({
			[query.sortBy]: sortOrder,
		});
	} else {
		sort.push({
			createdOrderDate: 'desc',
		});
	}

	if (hasText(query.fromDate) && hasText(query.toDate)) {
		whereClause.createdAt = {
			gte: moment(query.fromDate).startOf('day').toISOString(),
			lt: moment(query.toDate).endOf('day').toISOString(),
		};
	} else if (hasText(query.fromDate)) {
		whereClause.createdAt = {
			gte: moment(query.fromDate).startOf('day').toISOString(),
		};
	} else if (hasText(query.toDate)) {
		whereClause.createdAt = {
			lt: moment(query.toDate).endOf('day').toISOString(),
		};
	}

	if (hasText(query.paymentGateway)) {
		whereClause.paymentGateway = query.paymentGateway;
	}

	if (query.searchFilters && Array.isArray(query.searchFilters)) {
		query.searchFilters.forEach((filter) => {
			if (hasText(filter.field) && hasText(filter.value)) {
				whereClause[filter.field] = filter.value;
			}
		});
	}
	const b2cSynclog = await prisma.customerSynclog.findMany({
		skip: getSkipCount(query.pageNo, query.pageSize),
		take: Number(query.pageSize) || 10,
		orderBy: sort,
		where: whereClause,
		select: {
			// List all fields except syncHistory
			id: true,
			createdAt: true,
			createdOrderDate: true,
			paymentGateway: true,
			subTotalPrice: true,
			totalDiscount: true,
			totalTax: true,
			gatewayTransaction: true,
			totalPrice: true,
			hubspotProductId: true,
			hubspotQuoteId: true,
			hubspotStatus: true,
			hubspotContactId: true,
			hubspotDealId: true,
			xeroContactId: true,
			xeroInvoiceId: true,
			xeroInvoiceNumber: true,
			xeroPaymentId: true,
			xeroStatus: true,
			linnworksStatus: true,
			// Add other fields as needed, but exclude syncHistory
		},
	});

	const count = await prisma.customerSynclog.count({
		where: whereClause,
	});

	const content = b2cSynclog.map((e: any) => {
		return {
			...e,
			date: e?.createdAt,
			createdOrderDate: e?.createdOrderDate,
			paymentGateway: e?.paymentGateway,
			sales: e?.subTotalPrice,
			discounts: e?.totalDiscount,
			tax: e?.totalTax,
			refund: e?.totalRefund ? e.totalRefund : null,
			gatewayTransaction: e?.gatewayTransaction ? e.gatewayTransaction : null,
			payout: e?.totalPrice,
			hubspotProductId: e.hubspotProductId,
			hubspotQuoteId: e.hubspotQuoteId,
			hubspotStatus: e.hubspotStatus,
			hubspotContactId: e.hubspotContactId,
			hubspotDealId: e.hubspotDealId,
			xeroContactId: e.xeroContactId,
			xeroInvoiceId: e.xeroInvoiceId,
			xeroInvoiceNumber: e.xeroInvoiceNumber,
			xeroPaymentId: e.xeroPaymentId,
			xeroStatus: e.xeroStatus,
			linnworksStatus: e.linnworksStatus,
			hubspotDealUrl: e.hubspotDealId
				? `${process.env.HUBSPOT_DEAL_URL}/${e.hubspotDealId}`
				: null,
			hubspotContactUrl: e.hubspotContactId
				? `${process.env.HUBSPOT_CONTACT_URL}/${e.hubspotContactId}`
				: null,
			hubspotQuoteUrl: e.hubspotQuoteId
				? `${process.env.HUBSPOT_QUOTE_URL}/${e.hubspotQuoteId}`
				: null,
			xeroContactUrl: e.xeroContactId
				? `${process.env.XERO_CONTACT_URL}/${e.xeroContactId}`
				: null,
			xeroInvoiceUrl: e.xeroInvoiceId
				? `${process.env.XERO_INVOICE_URL}=${e.xeroInvoiceId}`
				: null,
			xeroPaymentUrl: e.xeroPaymentId
				? `${process.env.XERO_PAYMENT_URL}=${e.xeroPaymentId}`
				: null,
		};
	});

	return {
		content: content,
		count,
	};
}

async function getOrdersByPaymentGatewayAndDate(query: any) {
	const connection = await prisma.connection.findFirst();
	if (!connection) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}
	const connectionCredential = connection.shopifyCredentials;
	const organisation = JSON.parse(
		JSON.stringify(connectionCredential)
	).organisation.split('.')[0];

	const startDate = moment(query.date).startOf('day').toISOString();
	const endDate = moment(query.date).endOf('day').toISOString();

	const orders = await prisma.shopifyOrders.findMany({
		where: {
			createdOrderDate: {
				gte: startDate,
				lte: endDate,
			},
			paymentMethod: query.paymentGateway,
		},
		orderBy: { createdOrderDate: 'desc' },
	});
	const shopifyOrders = orders.map((order) => {
		return {
			...order,
			key: order.id,
			orderId: order.orderName,
			linnworksOrderId: order.linnworksOrderId,
			shopifyUrl: order.orderId
				? `${process.env.SHOPIFY_ORDER_URL}${organisation}/orders/${order.orderId}`
				: null,
			linnworksOrderUrl: order.linnworksOrderNum
				? `${process.env.LINNWORKS_ORDER_URL}${order.linnworksOrderNum}`
				: null,
		};
	});
	return shopifyOrders;
}

export const synclogService = {
	getSynclogs,
	getSyncLogHistoryById,
	getCOGSSynclogs,
	getB2CSynclogs,
	getB2CSyncLogHistoryById,
	getOrdersByPaymentGatewayAndDate,
};
