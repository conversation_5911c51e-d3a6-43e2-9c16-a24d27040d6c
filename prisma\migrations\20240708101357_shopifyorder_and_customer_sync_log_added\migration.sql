/*
  Warnings:

  - You are about to drop the `shopifyOrder` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "shopifyOrder";

-- CreateTable
CREATE TABLE "ShopifyOrders" (
    "id" TEXT NOT NULL,
    "orderId" TEXT,
    "status" TEXT,
    "currency" TEXT,
    "pricesIncludeTax" BOOLEAN,
    "discountTotal" TEXT,
    "shippingTotal" TEXT,
    "total" TEXT,
    "totalTax" TEXT,
    "type" TEXT,
    "connectionId" TEXT,
    "customerId" TEXT,
    "paymentMethod" TEXT,
    "paymentMethodTitle" TEXT,
    "transactionId" TEXT,
    "customerIpAddress" TEXT,
    "dateCompleted" TEXT,
    "datePaidGmt" TEXT,
    "createdOrderDate" TEXT,
    "modifiedOrderDate" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ShopifyOrders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CustomerSynclog" (
    "id" TEXT NOT NULL,
    "connectionId" TEXT NOT NULL,
    "shopifyOrderId" TEXT,
    "hubspotContactId" TEXT,
    "hubspotProductId" TEXT,
    "hubspotDealId" TEXT,
    "hubspotQuoteId" TEXT,
    "hubspotInvoiceId" TEXT,
    "linnworksOrderId" TEXT,
    "xeroInvoiceId" TEXT,
    "hubspotPaymentId" TEXT,
    "xeroPaymentId" TEXT,
    "xeroInvoiceNumber" TEXT,
    "hubSpotInvoiceNumber" TEXT,
    "subTotalPrice" TEXT,
    "totalDiscount" TEXT,
    "totalTax" TEXT,
    "paymentGateway" TEXT,
    "totalPrice" TEXT,
    "type" TEXT,
    "syncStatus" TEXT,
    "processingError" TEXT,
    "syncHistory" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CustomerSynclog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CustomerSynclog_id_key" ON "CustomerSynclog"("id");

-- AddForeignKey
ALTER TABLE "CustomerSynclog" ADD CONSTRAINT "CustomerSynclog_connectionId_fkey" FOREIGN KEY ("connectionId") REFERENCES "Connection"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
