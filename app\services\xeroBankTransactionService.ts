import axios from "axios";
import { prisma } from "../client/prisma";
import { IXeroContactProperties } from "../utils/xero-interface";

export async function createBankTransaction(connectionData: any, xeroContactId: string, stripeAmount: number) {

    const products = await prisma.paymentGateway.findFirst({
        where: {
            name: 'Stripe'
        }
    })

    if (!products) {
        throw new Error('Payment gateway not found');
    }

    const requestData = {
        Type: "RECEIVE",
        Contact: {
            ContactID: xeroContactId,
        },
        LineAmountTypes: "Exclusive",
        LineItems: [
            {
                Description: process.env.XERO_BANK_TRANSACTION_DESCRIPTION || 'Received from stripe',
                Quantity: 1,
                UnitAmount: stripeAmount,
                AccountCode: products.feesCode
            }
        ],
        BankAccount: {
            // Code: products.feesCode
            BankAccountNumber: process.env.XERO_BANK_ACCOUNT_NUMBER
        }
    };

    try {

        const response = await axios.put('https://api.xero.com/api.xro/2.0/BankTransactions', requestData, {
            headers: {
                Authorization: `Bearer ${connectionData?.xeroCredentials.access_token}`,
                "Xero-Tenant-Id": connectionData?.xeroCredentials.tenantId,
                Accept: "application/json"
            }
        });

        if (!response?.data?.BankTransactions.length) {
            return null;
        }

        return response?.data?.BankTransactions[0];

    } catch (error: any) {
        console.log('error: ', error);
    }
}

export async function checkForExistingContact(
    email: string,
    token: string,
    tenantId: string
) {
    try {
        const checkExistingInDb = await prisma.hubspotContacts.findFirst({
            where: {
                email,
            },
        });

        if (checkExistingInDb?.xeroContactId) {
            return checkExistingInDb.xeroContactId;
        }

        const checkInXero = await getXeroContact(email as string, token, tenantId);

        if (checkInXero) {
            const createContact = await prisma.hubspotContacts.update({
                where: {
                    email,
                },
                data: {
                    email,
                    xeroContactId: checkInXero.ContactID,
                },
            });

            return createContact.xeroContactId;
        }

        return null;
    } catch (error: any) {
        console.log('error: ', error);
    }
    return null;
}

export async function createXeroContact(
    connection: any,
    contactData: IXeroContactProperties
) {
    try {
        const _contactData = {
            EmailAddress: contactData.email,
            Name: contactData.name,
        };

        const xeroContact = await xeroCreateContact(
            _contactData,
            connection?.xeroCredentials.access_token,
            connection?.xeroCredentials.tenantId
        );

        if (!xeroContact) {
            return null;
        }

        const createContact = await prisma.hubspotContacts.create({

            data: {
                customerId: xeroContact.ContactID,
                email: contactData.email,
                xeroContactId: xeroContact.ContactID,
            },
        });
        return createContact.xeroContactId;
    } catch (error: any) {
        console.log('error: ', error);
    }

    return null;
}

//xero api call

export async function getXeroContact(
    contactId: string,
    token: string,
    tenantId: string
) {
    const params = {
        where: `ContactStatus="ACTIVE" AND EmailAddress = "${process.env.XERO_DEFAULT_EMAIL}"`
    };
    const contactResponse = await axios.get('https://api.xero.com/api.xro/2.0/Contacts', {
        headers: {
            Authorization: `Bearer ${token}`,
            "Xero-Tenant-Id": tenantId,
            Accept: "application/json",
        },
        params: params,
    });

    return contactResponse.data.Contacts[0];
}

export async function xeroCreateContact(
    data: any,
    token: string,
    tenantId: string
) {
    const contactResponse = await axios.put(
        'https://api.xero.com/api.xro/2.0/Contacts',
        data,
        {
            headers: {
                Authorization: `Bearer ${token}`,
                "Xero-Tenant-Id": `${tenantId}`,
                "Accept": "application/json",
                'Content-Type': 'application/json',
            },
        }
    );

    if (!contactResponse?.data?.Contacts[0].ContactID) {
        return null;
    }

    return contactResponse?.data?.Contacts[0];
}
