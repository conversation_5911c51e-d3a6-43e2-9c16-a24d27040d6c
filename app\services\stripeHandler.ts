import { Request, Response } from 'express';
import Stripe from 'stripe';
import { createJournal, updateJournal, xeroRefreshToken } from './xeroManualJournalService';
import { prisma } from '../client/prisma';
import { checkForExistingContact, createBankTransaction, createXeroContact } from './xeroBankTransactionService';
import moment from 'moment';
import { invokeFunctionService } from '../services/invokeFunctionService';

const stripe = new Stripe(process.env.STRIPE_TEST_KEY as string, {
    apiVersion: '2024-06-20',
});



export const stripePayoutHandler = async (req: Request, res: Response) => {
    const sig = req.headers['stripe-signature'];

    let event: Stripe.Event;
    const endpointSecret = process.env.STRIPE_ENDPOINT_SECRET as string;
    try {
        event = stripe.webhooks.constructEvent(req.body as string, sig as string, endpointSecret);
    } catch (err: any) {
        console.error('⚠️  Webhook signature verification failed.', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
        const connection: any = await prisma.connection.findFirst();
        if (!connection) {
            throw new Error('Connection not found');
        }

        const refreshToken = await xeroRefreshToken(connection);
        connection.xeroCredentials = refreshToken;

        switch (event.type) {
            case 'payout.paid': {
                const payout = event.data.object as Stripe.Payout;

                console.log(payout);

                if (!payout.amount) {
                    throw new Error('Payout amount not found');
                }

                let xeroContactId = await checkForExistingContact(
                    process.env.XERO_DEFAULT_EMAIL as string,
                    connection.xeroCredentials.access_token,
                    connection.xeroCredentials.tenantId
                );

                if (!xeroContactId) {
                    xeroContactId = await createXeroContact(connection, {
                        email: process.env.XERO_DEFAULT_EMAIL as string,
                        name: process.env.XERO_DEFAULT_NAME as string,
                    });
                }

                if (xeroContactId) {
                    const createXeroBankTransation = await createBankTransaction(connection, xeroContactId, payout.amount / 100);
                    console.log('createXeroBankTransation: ', createXeroBankTransation);
                }

                console.log(`Payout ${payout.id} was paid.`);
                break;
            }
            // Handle other event types as needed
            default:
                console.log(`Unhandled event type ${event.type}`);
        }

        res.json({ received: true });
    } catch (error: any) {
        console.error('Error handling event:', error.message);
        res.status(500).send(`Internal Server Error: ${error.message}`);
    }
};


export const stripeFeeTrasaction = async (req: Request, res: Response) => {
    const sig = req.headers['stripe-signature'];

    let event: Stripe.Event;
    const endpointSecret = process.env.STRIPE_ENDPOINT_SECRET_FEE as string;
    try {
        event = stripe.webhooks.constructEvent(req.body as string, sig as string, endpointSecret);
    } catch (err: any) {
        console.error('⚠️  Webhook signature verification failed.', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
        console.log('event: ', event);

        const connection: any = await prisma.connection.findFirst();
        if (!connection) {
            throw new Error('Connection not found');
        }

        const refreshToken = await xeroRefreshToken(connection);
        connection.xeroCredentials = refreshToken;


        switch (event.type) {
            case 'charge.succeeded': {
                const chargeCaptured = event.data.object as Stripe.Charge;

                const balanceTransaction = await stripe.balanceTransactions.retrieve(
                    chargeCaptured?.balance_transaction as string,
                );


                const feeDetails = balanceTransaction
                console.log('feeDetails: ', feeDetails);


                const existingJournal = await prisma.stripeJournalEntry.findFirst({
                    where: {
                        journalNo: `Stripe_${moment().format('DD_MMM_YYYY')}`,
                    },
                });

                console.log('existingJournal: ', existingJournal);
                if (existingJournal) {
                    const totalStripeAmount = Number(existingJournal.stripeAmount) + Number(feeDetails.fee / 100);
                    await updateJournal(
                        existingJournal.xeroJournalId,
                        totalStripeAmount,
                        connection.xeroCredentials.access_token,
                        connection.xeroCredentials.tenantId
                    );
                } else {
                    await createJournal(
                        (feeDetails.fee / 100),
                        connection.xeroCredentials.access_token,
                        connection.xeroCredentials.tenantId
                    );
                }

                break;
            }
            default:
                console.log(`Unhandled event type ${event.type}`);
        }
        res.json({ received: true });
    } catch (error: any) {
        console.error('Error handling event:', error.message);
        res.status(500).send(`Internal Server Error: ${error.message}`);
    }
};

export const stripeService = {
    stripePayoutHandler,
    stripeFeeTrasaction
};
