-- CreateTable
CREATE TABLE "XeroAccount" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "code" TEXT,
    "status" TEXT,
    "taxType" TEXT,
    "description" TEXT,
    "class" TEXT,
    "systemAccount" TEXT,
    "enablePaymentsToAccount" BOOLEAN,
    "showInExpenseClaims" BOOLEAN,
    "bankAccountType" TEXT,
    "reportingCode" TEXT,
    "reportingCodeName" TEXT,
    "updatedDateUTC" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "XeroAccount_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "XeroAccount_id_key" ON "XeroAccount"("id");

-- CreateIndex
CREATE UNIQUE INDEX "XeroAccount_accountId_key" ON "XeroAccount"("accountId");
