// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model MigrationLock {
  id       String  @id @unique @default(uuid())
  isLocked Boolean
  lockedBy String
  uid      String
  gid      String
  env      String
  os       String
}

model Migration {
  id       String @id @unique @default(uuid())
  username String
  uid      String
  gid      String
  env      String
  os       String
  name     String
}

model Role {
  id          String   @id @unique @default(uuid())
  name        String   @unique
  status      Boolean  @default(true)
  description String
  type        String
  users       Users[]
  createdBy   String?
  updatedBy   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Users {
  id                  String      @id @unique @default(uuid())
  name                String
  email               String
  status              Boolean     @default(true)
  password            String?
  roleId              String
  role                Role        @relation(fields: [roleId], references: [id])
  createdBy           String?
  updatedBy           String?
  forgotPasswordToken String?
  createdAt           DateTime    @default(now())
  updatedAt           DateTime    @updatedAt
  UserToken           UserToken[]
  isDeleted           Boolean     @default(false)
}

model UserToken {
  id        String   @id @unique @default(uuid())
  token     String
  userId    String
  user      Users    @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Connection {
  id                     String            @id @unique @default(uuid())
  hubSpotCredentials     Json?
  linnworksCredentials   Json?
  xeroCredentials        Json?
  wooCommerceCredentials Json?
  shopifyCredentials     Json?
  stripeCredentials      Json?
  createdAt              DateTime          @default(now())
  updatedAt              DateTime          @updatedAt
  createdBy              String?
  updatedBy              String?
  Synclog                Synclog[]
  CustomerSynclog        CustomerSynclog[]
  LastSyncDate           LastSyncDate[]
}

model Synclog {
  id                   String     @id @unique @default(uuid())
  connectionId         String
  connection           Connection @relation(fields: [connectionId], references: [id])
  wooCommerceOrderId   String?
  hubspotContactId     String?
  hubspotProductId     String?
  hubspotDealId        String?
  hubspotQuoteId       String?
  hubspotInvoiceId     String?
  linnworksOrderId     String?
  xeroInvoiceId        String?
  hubspotPaymentId     String?
  xeroPaymentId        String?
  xeroInvoiceNumber    String?
  hubSpotInvoiceNumber String?
  type                 String?
  syncStatus           String?
  processingError      String?
  syncHistory          Json?
  createdAt            DateTime   @default(now())
  updatedAt            DateTime   @updatedAt
  invoice              Invoices?
}

model COGSJournalEntry {
  id            String   @id @unique @default(uuid())
  cogsAmount    String
  xeroJournalId String
  journalNo     String   @unique
  journalMonth  String
  journalDate   String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model LastSyncDate {
  id           String     @id @unique @default(uuid())
  connectionId String
  connection   Connection @relation(fields: [connectionId], references: [id])
  moduleType   String     @unique
  date         DateTime
}

model Invoices {
  id                       String         @id @unique @default(uuid())
  wooCommerceCustomer      Json?
  wooCommerceShipping      Json?
  wooCommerceOrderId       String?
  hubSpotInvoiceId         String?
  hubSpotContactId         String?
  xeroContactId            String?
  hubSpotDealId            String?
  hubSpotQuoteId           String?
  linnworksOrderId         String?
  xeroInvoiceId            String?
  wooCommerceOrderNumber   String?
  wooCommerceAmount        String?
  wooCommerceShippingTotal String?
  wooCommerceShippingTax   String?
  wooCommerceDiscountTotal String?
  wooCommerceDiscountTax   String?
  wooCommerceCarTax        String?
  wooCommerceTotalTax      String?
  wooCommerceOrderDate     DateTime?
  hubSpotPaymentStatus     String?
  hubSpotPaymentId         String?
  xeroPaymentId            String?
  postalTrackingNumber     String?
  trackingUrl              String?
  invoiceStatus            String?
  xeroInvoiceNumber        String?
  hubSpotInvoiceNumber     String?
  linnworksOrderNum        Int?
  hubSpotPaymentRefNo      String?
  hubSpotPaymentNotes      String?
  syncLog                  Synclog        @relation(fields: [synclogId], references: [id])
  synclogId                String         @unique
  createdAt                DateTime       @default(now())
  updatedAt                DateTime       @updatedAt
  InvoiceLines             InvoiceLines[]
}

model InvoiceLines {
  id                   String   @id @unique @default(uuid())
  lineId               String?
  invoiceId            String
  invoices             Invoices @relation(fields: [invoiceId], references: [id])
  description          String?
  quantity             Int?
  unitPrice            Float?
  totalPrice           Float?
  subTotal             Float?
  wooCommerceProductId String?
  hubSpotProductId     String?
  linnWorksInventoryId String?
  xeroItemId           String?
  hubSpotLineId        String?
  sku                  String?
  productName          String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
}

model HubspotContacts {
  id            String   @id @unique @default(uuid())
  customerId    String
  email         String   @unique
  dealId        String?
  xeroContactId String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model HubspotProducts {
  id                   String   @id @unique @default(uuid())
  name                 String?
  sku                  String?
  unitPrice            Float?
  hsProductId          String?
  wooCommerceProductId String?
  linnWorksInventoryId String?
  xeroItemId           String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
}

model ShopifyOrders {
  id                 String   @id @unique @default(uuid())
  orderId            String   @unique
  orderName          String?
  status             String?
  currency           String?
  pricesIncludeTax   Boolean?
  discountTotal      String?
  shippingTotal      String?
  total              String?
  totalTax           String?
  type               String?
  connectionId       String?
  customerId         String?
  paymentMethod      String?
  paymentMethodTitle String?
  transactionId      String?
  customerIpAddress  String?
  dateCompleted      String?
  datePaidGmt        String?
  createdOrderDate   String?
  modifiedOrderDate  String?
  productLineItems   Json?
  shopifyCustomer    Json?
  linnworksOrderId   String?
  linnworksOrderNum  Int?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
}

model CustomerSynclog {
  id                   String     @id @unique @default(uuid())
  connectionId         String
  connection           Connection @relation(fields: [connectionId], references: [id])
  shopifyOrderId       String?    @unique
  hubspotContactId     String?
  hubspotProductId     String?
  hubspotDealId        String?
  hubspotQuoteId       String?
  hubspotInvoiceId     String?
  linnworksOrderId     String?
  xeroInvoiceId        String?
  hubspotPaymentId     String?
  xeroPaymentId        String?
  xeroInvoiceNumber    String?
  hubSpotInvoiceNumber String?
  xeroContactId        String?
  subTotalPrice        String?
  totalDiscount        String?
  totalTax             String?
  paymentGateway       String?
  gatewayTransaction   String?
  totalPrice           String?
  type                 String?
  hubspotStatus        String?
  xeroStatus           String?
  linnworksStatus      String?
  syncStatus           String?
  processingError      String?
  syncHistory          Json?
  createdOrderDate     String?
  modifiedOrderDate    String?
  createdAt            DateTime   @default(now())
  updatedAt            DateTime   @updatedAt
}

model shopifyProducts {
  id                   String   @id @unique @default(uuid())
  connectionId         String
  shopifyProductId     String?
  shopifyVariantId     String?
  linnWorksInventoryId String?
  xeroItemId           String?
  title                String?
  vendorName           String?
  body_html            String?
  sku                  String?
  unitPrice            String?
  purchasePrice        String?
  salesCOA             String?
  purchaseCOA          String?
  tax                  String?
  productType          String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
}

model XeroAccount {
  id                      String    @id @unique @default(uuid())
  accountId               String    @unique
  name                    String
  type                    String
  code                    String?
  status                  String?
  taxType                 String?
  description             String?
  class                   String?
  systemAccount           String?
  enablePaymentsToAccount Boolean?
  showInExpenseClaims     Boolean?
  bankAccountType         String?
  reportingCode           String?
  reportingCodeName       String?
  updatedDateUTC          DateTime?
  createdAt               DateTime  @default(now())
  updatedAt               DateTime  @updatedAt
}

model HubspotShopifyProducts {
  id                   String   @id @unique @default(uuid())
  name                 String?
  sku                  String?
  unitPrice            Float?
  hsProductId          String?
  shopifyVariantId     String?
  linnWorksInventoryId String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
}

model paymentGateway {
  id          String   @id @unique @default(uuid())
  name        String
  coaAccount  String?
  coaCode     String?
  feesAccount String?
  feesCode    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model stripeJournalEntry {
  id            String   @id @unique @default(uuid())
  stripeAmount  String
  xeroJournalId String
  journalNo     String   @unique
  journalMonth  String
  journalDate   String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}
