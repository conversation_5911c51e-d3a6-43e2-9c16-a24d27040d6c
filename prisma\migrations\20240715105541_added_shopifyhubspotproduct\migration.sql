-- CreateTable
CREATE TABLE "HubspotShopifyProducts" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "sku" TEXT,
    "unitPrice" DOUBLE PRECISION,
    "hsProductId" TEXT,
    "shopifyVariantId" TEXT,
    "linnWorksInventoryId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "HubspotShopifyProducts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "HubspotShopifyProducts_id_key" ON "HubspotShopifyProducts"("id");
