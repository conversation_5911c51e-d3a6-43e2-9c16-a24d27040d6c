import { prisma } from "../client/prisma";

async function getProducts(query: any) {
    const month = parseInt(query.month);
    const currentYear = new Date().getFullYear();

    const data: any = await prisma.$queryRaw`
        WITH filtered_data AS (
            SELECT 
                "productName", 
                quantity
            FROM 
                public."InvoiceLines"
            WHERE
                EXTRACT(YEAR FROM "createdAt") = ${currentYear} AND
                EXTRACT(MONTH FROM "createdAt") = ${month}
        )
        SELECT 
            "productName", 
            CAST(SUM(quantity) AS INTEGER) AS totalQuantity,
            ROUND(SUM(quantity) * 100.0 / (SELECT SUM(quantity) FROM filtered_data), 3) AS percentage
        FROM 
            filtered_data
        GROUP BY 
            "productName"
    `;

    return data;

}

async function getSales() {

    const data: any = await prisma.$queryRaw`
                WITH months AS (
            SELECT 
                DATE_TRUNC('month', NOW()) - INTERVAL '1 month' * generate_series(0, 5) AS month
        )
        SELECT 
            TO_CHAR(months.month, '<PERSON>Month YYYY') AS month,
            COALESCE(SUM(CAST("wooCommerceAmount" AS NUMERIC)), 0) AS totalSales
        FROM 
            months
        LEFT JOIN 
            public."Invoices" ON DATE_TRUNC('month', "createdAt") = months.month
        GROUP BY 
            months.month
        ORDER BY 
            months.month ASC

            `;
    if (!data.length) {
        return null;
    }

	return data;
}

async function getSalesCount(query: any) {
	const month = parseInt(query.month);
	const currentYear = new Date().getFullYear();
	const data: any = await prisma.$queryRaw`
    SELECT 
        TRIM(TO_CHAR(DATE_TRUNC('month', CAST("createdAt" AS DATE)), 'Month')) AS month,
        CAST(COUNT("id") AS INTEGER) AS totalSales
    FROM 
        public."Invoices"
    WHERE
        EXTRACT(YEAR FROM "createdAt") = ${currentYear} AND
        EXTRACT(MONTH FROM "createdAt") = ${month}
    GROUP BY 
        DATE_TRUNC('month', CAST("createdAt" AS DATE))
    ORDER BY 
        DATE_TRUNC('month', CAST("createdAt" AS DATE)) ASC
    `;

	if (!data.length) {
		return null;
	}

	return data;
}

async function getOrderAndSalesCount(query: any) {
	const month = parseInt(query.month);
	const currentYear = new Date().getFullYear();
const data: any = await prisma.$queryRaw`
    SELECT month,
        CAST(COUNT(totalSales) AS INTEGER) AS totalSales,
        CAST(COUNT(id) AS INTEGER) AS totalOrders,
        CASE
            WHEN CAST(COUNT(totalSales) AS INTEGER) = 0 THEN 0
            WHEN (SUM(COUNT(totalSales)) OVER () + SUM(COUNT(id)) OVER ()) = 0 THEN 0
            ELSE ROUND((CAST(COUNT(totalSales) AS DECIMAL) / (SUM(COUNT(totalSales)) OVER () + SUM(COUNT(id)) OVER ())) * 100, 2)
        END AS totalSalesPercentage,
        CASE
            WHEN CAST(COUNT(id) AS INTEGER) = 0 THEN 0
            WHEN (SUM(COUNT(id)) OVER () + SUM(COUNT(totalSales)) OVER ()) = 0 THEN 0
            ELSE ROUND((CAST(COUNT(id) AS DECIMAL) / (SUM(COUNT(id)) OVER () + SUM(COUNT(totalSales)) OVER ())) * 100, 2)
        END AS totalOrdersPercentage
    FROM (
        SELECT 
            TO_CHAR(DATE_TRUNC('month', "createdAt"::timestamp), 'FMMonth YYYY') AS month,
            CAST("wooCommerceAmount" AS NUMERIC) AS totalSales,
            NULL AS id
        FROM 
            public."Invoices"
        WHERE
            EXTRACT(YEAR FROM "createdAt"::timestamp) = ${currentYear} AND
            EXTRACT(MONTH FROM "createdAt"::timestamp) = ${month}
        UNION ALL
        SELECT 
            TO_CHAR(DATE_TRUNC('month', "createdOrderDate"::timestamp), 'FMMonth YYYY') AS month,
            NULL AS totalSales,
            id
        FROM 
            public."ShopifyOrders"
        WHERE
            EXTRACT(YEAR FROM "createdOrderDate"::timestamp) = ${currentYear} AND
            EXTRACT(MONTH FROM "createdOrderDate"::timestamp) = ${month}
    ) AS combined
    GROUP BY 
        month
    ORDER BY 
        month ASC
`;

	if (!data.length) {
		return null;
	}
	console.log(data);
	const totalsales = data.reduce((total: number, item: { totalsales: number }) => total + item.totalsales, 0);
	const totalorders = data.reduce((total: number, item: { totalorders: number }) => total + item.totalorders, 0);
	const salesPercentage = data.reduce((total: number, item: { totalsalespercentage: number }) => total + item.totalsalespercentage, 0);
	const orderPercentage = data.reduce((total: number, item: { totalorderspercentage: number }) => total + item.totalorderspercentage, 0);
	return {
		totalsales,
		totalorders,
		salesPercentage,
		orderPercentage,
	};
}

async function getShopifyOrderCount(query: any) {
	const month = parseInt(query.month);
	const currentYear = new Date().getFullYear();
	const data: any = await prisma.$queryRaw`
    SELECT 
        TO_CHAR(CAST("createdOrderDate" AS DATE), 'Month') AS month,
        COUNT(id)::text AS totalsales
    FROM 
        public."ShopifyOrders"
    WHERE
        EXTRACT(YEAR FROM CAST("createdOrderDate" AS DATE)) = ${currentYear} AND
        EXTRACT(MONTH FROM CAST("createdOrderDate" AS DATE)) = ${month}
    GROUP BY 
        TO_CHAR(CAST("createdOrderDate" AS DATE), 'Month')
    ORDER BY 
        MIN(CAST("createdOrderDate" AS DATE))
`;

	if (!data.length) {
		return null;
	}

	return data;
}

async function getShopifySales() {
	const data: any = await prisma.$queryRaw`
    WITH months AS (
    SELECT 
        DATE_TRUNC('month', NOW()) - INTERVAL '1 month' * generate_series(0, 5) AS month
        )
    SELECT 
        TO_CHAR(months.month, 'FMMonth YYYY') AS month,
        SUM(CAST(total AS numeric(10,2))) AS totalSales  
    FROM 
        months
    LEFT JOIN 
        public."ShopifyOrders" ON DATE_TRUNC('month', CAST("createdOrderDate" AS DATE)) = months.month 
    GROUP BY
        months.month
    ORDER BY 
        months.month ASC
    `;
	if (data.length === 0) {
		return null;
	}
	return data;
}

export const dashboardService = {
	getProducts,
	getSales,
	getShopifyOrderCount,
	getShopifySales,
	getSalesCount,
	getOrderAndSalesCount,
};
