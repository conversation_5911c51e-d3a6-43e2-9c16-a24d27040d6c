export function invalidText(value: any) {
	return (
		value == null || value == undefined || value.toString().trim().length == 0
	);
}

// Remove null, undefined and empty keys
export function removeNullKeys(obj: any) {
	let isNullKey = false;
	for (const key in obj) {
		if (invalidText(obj[key])) {
			isNullKey = true;
			delete obj[key];
		}
	}
	return { obj, isNullKey };
}

export function isOneYearAgo(date: Date) {
	// Get today's date
	const currentDate = new Date();

	// Subtract one year from the current date
	const oneYearAgo = new Date(currentDate);
	oneYearAgo.setFullYear(currentDate.getFullYear() - 1);

	// Convert both dates to milliseconds since epoch
	const dateMs = date.getTime();
	const oneYearAgoMs = oneYearAgo.getTime();

	// Compare the given date with one year ago
	return dateMs < oneYearAgoMs;
}

export function findFieldByName(data: any, name: string) {
	for (const key in data) {
		if (data[key].Name === name) {
			return data[key];
		}
	}
	return null;
}

export const getLastFiveYears = () => {
	const currentYear = new Date().getFullYear();

	return Array.from({ length: 5 }, (_, index) => currentYear - index);
};

export const validateMonth = (month: number) => {
	if (Number(month) > 0 && Number(month) <= 13) {
		return true;
	}
	return false;
};

export const getLastFifthYear = () => {
	const today = new Date();
	const lastFifthYear = today.getFullYear() - 4;
	today.setFullYear(lastFifthYear);

	return `${today.getFullYear()}-01-01`;
};

export const formatNumber = (number: number) => {
	if (!invalidText(number) && typeof number === 'number') {
		const numberWithSeparator = Number(number.toFixed(2)).toLocaleString();

		let strNumber = numberWithSeparator.toString();

		// Check if the number already has 2 decimal places
		if (strNumber.indexOf('.') === -1) {
			// If not, add two decimal places
			strNumber += '.00';
		} else if (strNumber.split('.')[1].length === 1) {
			// If there's only one decimal place, add one more
			strNumber += '0';
		}
		return strNumber;
	}
	return number;
};

export const getSkipCount = (pageNo: number = 1, pageSize: number = 10) => {
	return (Number(pageNo) - 1) * Number(pageSize)
}

export function isBlank(value?: string | number | null) {
	return (
		null === value ||
		undefined === value ||
		value.toString().trim().length === 0
	);
}

export function hasText(value?: string) {
	return !isBlank(value);
}