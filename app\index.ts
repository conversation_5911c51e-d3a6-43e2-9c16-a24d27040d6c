// eslint-disable-next-line @typescript-eslint/no-var-requires
require('dotenv').config();
import express from 'express';
import md5 from 'md5';
import { v4 as uuidv4 } from 'uuid';
import { RequestExtended } from './interfaces/global';
import routes from './routes';
import { logger } from './utils/logger';
import { migrate } from './migration';
import cors from 'cors';
import { Server } from 'socket.io';
import http from 'http';
import { verifyAccessToken } from './helpers/tokenHelper';
import { prisma } from './client/prisma';
import stripeRoutes from './routes/stripeRoutes';

const PORT = process.env.PORT || 8000;

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
	path: '/api/web-socket/',
	cors: {
		origin: ['http://************', 'http://localhost:3000'],
	},
});

(global as any).io = io;

io.use(async (socket, next) => {
	const token: string = socket.handshake.query.token as string;
	console.log(token)
	if (token) {
		const isExist = await prisma.userToken.findFirst({
			where: {
				token,
			},
		});

		if (!isExist) {
			throw new Error('Authentication error');
		}

		const payload: any = verifyAccessToken(token);

		if (!payload) {
			throw new Error('Authentication error');
		}

		(socket as any).user = {
			id: payload.id,
			email: payload.email,
			type: payload.type,
			roleId: payload.roleId,
			name: payload.name,
		};

		next();
	} else {
		next(new Error('Authentication error'));
	}
});

io.on('connection', (socket) => {
	console.log('New client connected:', (socket as any).user);

	socket.on('disconnect', () => {
		console.log('Client disconnected');
	});

	socket.emit('message', 'Welcome to the authenticated Socket.IO server!');
});

app.use(cors());
app.use('/api/v1/stripe', express.raw({ type: 'application/json' }));

app.use((req: RequestExtended, res, next) => {
	req.id = md5(uuidv4());
	req.traceId = req.header('eg-request-id') || '-';
	req.logId = [
		`traceId[${req?.traceId}]`,
		`spanId[${req?.id}]`,
		`user[${req?.user?.id ? req?.user?.id + ',' + req?.user?.type : '-'}]`,
	].join(' ');
	req.log = (...args: any[]) => {
		logger.info([new Date().toISOString(), req.logId, ...args].join(' '));
	};
	req.error = (...args: any[]) => {
		logger.error([new Date().toISOString(), req.logId, ...args].join(' '));
	};
	next();
});

app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Run migrations
migrate().catch((err) => {
	logger.error('Error while running migrations', err);
});

app.use('/api/v1', routes);
app.use('/api/v1/stripe', stripeRoutes);
server.listen(PORT, () => {
	logger.info('Server is listening on port ' + PORT);
});
