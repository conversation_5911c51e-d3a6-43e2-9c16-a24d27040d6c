-- CreateTable
CREATE TABLE "shopifyProducts" (
    "id" TEXT NOT NULL,
    "connectionId" TEXT NOT NULL,
    "shopifyProductId" TEXT,
    "shopifyVariantId" TEXT,
    "linnWorksInventoryId" TEXT,
    "title" TEXT,
    "vendorName" TEXT,
    "body_html" TEXT,
    "sku" TEXT,
    "unitPrice" TEXT,
    "purchasePrice" TEXT,
    "salesCOA" TEXT,
    "purchaseCOA" TEXT,
    "tax" TEXT,
    "productType" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "shopifyProducts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "shopifyProducts_id_key" ON "shopifyProducts"("id");
