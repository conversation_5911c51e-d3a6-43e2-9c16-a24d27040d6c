{"info": {"_postman_id": "d773e23e-c547-4457-a84c-459c52255c11", "name": "<PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "27033781"}, "item": [{"name": "users", "item": [{"name": "User Invite", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{AccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Test User\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"roleId\": \"8217d807-6114-4ded-a182-654ea5970fa0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/users/invite", "host": ["{{BaseUrl}}"], "path": ["users", "invite"]}}, "response": []}, {"name": "reinvite user", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/users/reinvite", "host": ["{{BaseUrl}}"], "path": ["users", "reinvite"]}}, "response": []}, {"name": "user status update", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "url": {"raw": "{{BaseUrl}}/users/c979a985-c60b-461b-aa80-b714bba876ca/status/Active", "host": ["{{BaseUrl}}"], "path": ["users", "c979a985-c60b-461b-aa80-b714bba876ca", "status", "Active"]}}, "response": []}, {"name": "user delete", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "url": {"raw": "{{BaseUrl}}/users/delete/ff34f86a-88bc-4444-afc7-882a2c04b2b0", "host": ["{{BaseUrl}}"], "path": ["users", "delete", "ff34f86a-88bc-4444-afc7-882a2c04b2b0"]}}, "response": []}]}, {"name": "roles", "item": [{"name": "role update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{AccessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "{{BaseUrl}}/roles/f740bbe0-c7f8-400c-88db-a5d2ac6646f9/status/Active", "host": ["{{BaseUrl}}"], "path": ["roles", "f740bbe0-c7f8-400c-88db-a5d2ac6646f9", "status", "Active"]}}, "response": []}, {"name": "role list", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{AccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BaseUrl}}/roles", "host": ["{{BaseUrl}}"], "path": ["roles"]}}, "response": []}]}, {"name": "auth", "item": [{"name": "reset-password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"password\": \"Satva1214#\",\r\n    \"confirmPassword\": \"Satva1214#\",\r\n    \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.UyBt6LfGbYNUHqFvSQrKqouwQak7qISnT4uYfJvqrgk\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/auth/reset-password", "host": ["{{BaseUrl}}"], "path": ["auth", "reset-password"]}}, "response": []}, {"name": "forgot-password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/auth/forgot-password", "host": ["{{BaseUrl}}"], "path": ["auth", "forgot-password"]}}, "response": []}, {"name": "login", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Satva1213#\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/auth/login", "host": ["{{BaseUrl}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "url": {"raw": "{{BaseUrl}}/auth/logout", "host": ["{{BaseUrl}}"], "path": ["auth", "logout"]}}, "response": []}]}, {"name": "connections", "item": [{"name": "woocommerce auth", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"baseurl\": \"https://satvasolutions.com/woocommerce-test/\",\r\n  \"consumer_key\": \"ck_24554006fcfe529f3682ab4317c5431f4c6b1f38\",\r\n  \"consumer_secret\": \"cs_ce22d59036463ea91579bbf6e4ca054ef4c02e36\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/connection/woocommerce/auth", "host": ["{{BaseUrl}}"], "path": ["connection", "woocommerce", "auth"]}}, "response": []}, {"name": "xero auth", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "url": {"raw": "{{BaseUrl}}/connection/xero/auth", "host": ["{{BaseUrl}}"], "path": ["connection", "xero", "auth"]}}, "response": []}, {"name": "get connection", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "url": {"raw": "{{BaseUrl}}/connection", "host": ["{{BaseUrl}}"], "path": ["connection"]}}, "response": []}, {"name": "hubspot auth", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "body": {"mode": "raw", "raw": "{\r\n    \"hapikey\":\"********************************************\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/connection/hubspot/auth", "host": ["{{BaseUrl}}"], "path": ["connection", "hubspot", "auth"]}}, "response": []}, {"name": "xero callback", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"url\":\"http://localhost:3000/xero/callback?code=aGXcYmU0c1pBMj1dyOI1YLuU-OkPg1tmxshIwF_Urv4&scope=openid&session_state=wxeiNiO3kDtbY_3MaN3p-HYL-tFKctx9sq86bVORbvc.x8FmJ_1gXF-avTpC030hyA\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/connection/xero/callback", "host": ["{{BaseUrl}}"], "path": ["connection", "xero", "callback"]}}, "response": []}, {"name": "create contact hubspot", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "body": {"mode": "raw", "raw": "{\r\n    \"properties\": {\r\n        \"email\": \"<EMAIL>\",\r\n        \"firstname\": \"test2\",\r\n        \"lastname\": \"rob2\",\r\n        \"phone\": \"(*************\",\r\n        \"company\": \"HubSpot\",\r\n        \"website\": \"hubspot.com\",\r\n        \"lifecyclestage\": \"marketingqualifiedlead\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/connection/hubspot/contact/create", "host": ["{{BaseUrl}}"], "path": ["connection", "hubspot", "contact", "create"]}}, "response": []}, {"name": "create product hubspot", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "body": {"mode": "raw", "raw": "{\r\n    \"properties\": {\r\n        \"name\": \"test4\",\r\n        \"price\": \"70.00\",\r\n        \"hs_sku\": \"test4\",\r\n        \"description\": \"Onboarding service for data product\",\r\n        \"hs_cost_of_goods_sold\": \"10.00\",\r\n        \"hs_recurring_billing_period\": \"P12M\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/connection/hubspot/product", "host": ["{{BaseUrl}}"], "path": ["connection", "hubspot", "product"]}}, "response": []}, {"name": "create deal hubspot", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "body": {"mode": "raw", "raw": "{\r\n    \"properties\": {\r\n        \"amount\": \"1500.00\",\r\n        \"closedate\": \"2019-12-07T16:50:06.678Z\",\r\n        \"dealname\": \"test1\",\r\n        \"pipeline\": \"default\",\r\n        \"dealstage\": \"contractsent\",\r\n        \"hubspot_owner_id\": \"1240021524\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/connection/hubspot/deal", "host": ["{{BaseUrl}}"], "path": ["connection", "hubspot", "deal"]}}, "response": []}, {"name": "create quotes hubspot", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"properties\": {\r\n    \"hs_title\": \"CustomerName - annual SEO audit\",\r\n    \"hs_expiration_date\": \"2024-01-01\"\r\n  },\r\n  \"associations\": [\r\n        {\r\n            \"to\": {\r\n                \"id\": \"123456\",\r\n                \"type\": \"contact\"\r\n            },\r\n            \"types\": [\r\n                {\r\n                    \"associationCategory\": \"HUBSPOT_DEFINED\",\r\n                    \"associationTypeId\": 64 \r\n                }\r\n            ]\r\n        }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/connection/hubspot/quote", "host": ["{{BaseUrl}}"], "path": ["connection", "hubspot", "quote"]}}, "response": []}, {"name": "read contact hubspot", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "url": {"raw": "{{BaseUrl}}/connection/hubspot/contact/read?contactId=22842032309", "host": ["{{BaseUrl}}"], "path": ["connection", "hubspot", "contact", "read"], "query": [{"key": "contactId", "value": "22842032309"}]}}, "response": []}, {"name": "https://api.hubapi.com/crm/v3/objects/deals", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer ********************************************"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "https://api.hubapi.com/crm/v3/objects/deals", "protocol": "https", "host": ["api", "<PERSON><PERSON><PERSON>", "com"], "path": ["crm", "v3", "objects", "deals"]}}, "response": []}, {"name": "https://api.hubapi.com/crm/v3/owners", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer ********************************************"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "https://api.hubapi.com/crm/v3/owners", "protocol": "https", "host": ["api", "<PERSON><PERSON><PERSON>", "com"], "path": ["crm", "v3", "owners"]}}, "response": []}]}, {"name": "invokefunctions", "item": [{"name": "woocommerceOrder", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "url": {"raw": "{{BaseUrl}}/invoke/woocommerce/order", "host": ["{{BaseUrl}}"], "path": ["invoke", "woocommerce", "order"]}}, "response": []}]}, {"name": "synclog", "item": [{"name": "synclog", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{AccessToken}}"}], "url": {"raw": "{{BaseUrl}}/synclogs?sortBy=wooCommerceOrderId&sortOrder=desc&pageNo=1&pageSize=20", "host": ["{{BaseUrl}}"], "path": ["synclogs"], "query": [{"key": "sortBy", "value": "wooCommerceOrderId"}, {"key": "sortOrder", "value": "desc"}, {"key": "text", "value": "208", "disabled": true}, {"key": "pageNo", "value": "1"}, {"key": "pageSize", "value": "20"}]}}, "response": []}]}]}