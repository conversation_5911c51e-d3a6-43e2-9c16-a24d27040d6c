-- CreateTable
CREATE TABLE "Connection" (
    "id" TEXT NOT NULL,
    "hubSpotCredentials" JSONB,
    "linnworksCredentials" JSONB,
    "xeroCredentials" JSONB,
    "wooCommerceCredentials" JSONB,
    "shopifyCredentials" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,

    CONSTRAINT "Connection_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Connection_id_key" ON "Connection"("id");
