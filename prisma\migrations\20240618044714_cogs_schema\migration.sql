-- CreateTable
CREATE TABLE "COGSJournalEntry" (
    "id" TEXT NOT NULL,
    "cogsAmount" TEXT NOT NULL,
    "journalNo" TEXT NOT NULL,
    "journalMonth" TEXT NOT NULL,
    "journalDate" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "COGSJournalEntry_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "COGSJournalEntry_id_key" ON "COGSJournalEntry"("id");

-- CreateIndex
CREATE UNIQUE INDEX "COGSJournalEntry_journalNo_key" ON "COGSJournalEntry"("journalNo");
