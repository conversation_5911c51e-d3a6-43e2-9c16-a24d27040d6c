/*
  Warnings:

  - A unique constraint covering the columns `[id]` on the table `ShopifyOrders` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[orderId]` on the table `ShopifyOrders` will be added. If there are existing duplicate values, this will fail.
  - Made the column `orderId` on table `ShopifyOrders` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "ShopifyOrders" ALTER COLUMN "orderId" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "ShopifyOrders_id_key" ON "ShopifyOrders"("id");

-- CreateIndex
CREATE UNIQUE INDEX "ShopifyOrders_orderId_key" ON "ShopifyOrders"("orderId");
