/* eslint-disable camelcase */
import axios from 'axios';
import { prisma } from '../client/prisma';
import { IWoocommerceData } from '../interfaces/woocommerceInterface';
import ApiException from '../utils/errorHandler';
import { ErrorCodes } from '../utils/response';
import { invalidText } from '../utils/utils';

async function woocommerceAuthService(data: IWoocommerceData) {
	try {
		console.log(
			`${data.baseurl}/wp-json/wc/v3/orders`,
			{
				auth: {
					username: data.consumer_key,
					password: data.consumer_secret,
				},
			})
		const authresponse: any = await axios.get(
			`${data.baseurl}/wp-json/wc/v3/orders`,
			{
				auth: {
					username: data.consumer_key,
					password: data.consumer_secret,
				},
			}
		);
		console.log(authresponse)
		if (authresponse.data.length) {
			const siteInfoResponse = await axios.get(
				`${data.baseurl}/wp-json`,
				{
					auth: {
						username: data.consumer_key,
						password: data.consumer_secret,
					},
				}
			);
			const organisation = siteInfoResponse.data.name;

			await prisma.connection.update({
				where: {
					id: process.env.CONNECTION_ID,
				},
				data: {
					wooCommerceCredentials: { ...data as any, organisation },
				},
			});
			return {
				success: true,
			};
		}
	} catch (error) {
		console.log(error)
		throw new ApiException(ErrorCodes.INVALID_WOOCOMMERCE_CREDENTIALS);
	}
}

async function xeroGetAuthUrlService() {
	const clientId = process.env.XERO_CLIENT_ID;
	const redirectUri = process.env.XERO_REDIRECT_URI;
	const authorizationUrl = 'https://login.xero.com/identity/connect/authorize';
	const state = process.env.XERO_STATE;
	const scope = process.env.XERO_SCOPE;

	const authUrl = `${authorizationUrl}?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&state=${state}`;

	return {
		authUrl,
	};
}

async function xeroRedirectUrlService(data: any) {
	const clientId = process.env.XERO_CLIENT_ID;
	const clientSecret = process.env.XERO_CLIENT_SECRET;
	const redirectUri = process.env.XERO_REDIRECT_URI;

	const urlString = data.url;
	const url = new URL(urlString);
	const authorizationCode = url.searchParams.get('code');
	const authString = clientId + ':' + clientSecret;
	const encodedAuthString = Buffer.from(authString).toString('base64');
	const authHeaderValue = 'Basic ' + encodedAuthString;

	const requestData = {
		grant_type: 'authorization_code',
		redirect_uri: redirectUri,
		code: authorizationCode,
	};

	const axiosConfig = {
		headers: {
			Accept: 'application/json',
			Authorization: authHeaderValue,
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	};

	try {
		const authResponse = await axios.post(
			'https://identity.xero.com/connect/token',
			requestData,
			axiosConfig
		);

		if (
			authResponse.data &&
			!invalidText(authResponse.data.access_token) &&
			!invalidText(authResponse.data.refresh_token)
		) {
			const xeroTenantResponse = await axios.get('https://api.xero.com/connections', {
				headers: {
					'Authorization': `Bearer ${authResponse.data.access_token}`,
					'Content-Type': 'application/json'
				}
			});


			const xeroOrganisationResponse = await axios.get('https://api.xero.com/api.xro/2.0/Organisation', {
				headers: {
					'Authorization': `Bearer ${authResponse.data.access_token}`,
					'Content-Type': 'application/json',
					'Xero-Tenant-Id': xeroTenantResponse.data[0]?.tenantId,
				}
			});
			const tenantId = xeroTenantResponse.data[0]?.tenantId;
			const organisation = xeroOrganisationResponse.data?.Organisations[0]?.Name;

			await prisma.connection.update({
				where: {
					id: process.env.CONNECTION_ID,
				},
				data: {
					xeroCredentials: { ...authResponse.data, tenantId, organisation },
				},
			});
			return {
				success: true,
			};
		}
	} catch (error) {
		throw new ApiException(ErrorCodes.INVALID_XERO_CALLBACK);
	}
}

async function hubspotAuthService(data: any) {
	const hapikey = data.hapikey;
	const hubspotVerifyApi = process.env.HUBSPOT_VERIFY_API_KEY;

	try {
		const authResponse = await axios.get(hubspotVerifyApi as string, {
			headers: {
				Authorization: `Bearer ${hapikey}`,
			},
		});
		if (authResponse.status === 200) {
			const ownerData = await axios.get(
				'https://api.hubapi.com/crm/v3/owners/',
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
					},
				}
			);

			let ownerId;

			if (ownerData.data?.results?.length) {
				ownerId = ownerData.data.results[0].id
			}


			const companyData = await axios.get(
				'https://api.hubapi.com/companies/v2/companies',
				{
					headers: {
						Authorization: `Bearer ${hapikey}`,
						'Content-Type': 'application/json'
					},
				}
			);
			let organisation;

			if (companyData.data.companies && companyData.data.companies.length) {
				organisation = companyData.data.companies[0].properties.name.value
			}


			await prisma.connection.update({
				where: {
					id: process.env.CONNECTION_ID,
				},
				data: {
					hubSpotCredentials: { hapikey: hapikey, ownerId, organisation },
				},
			});
			return {
				success: true,
			};
		}
	} catch (error) {
		throw new ApiException(ErrorCodes.INVALID_HUBSPOT_CREDENTIALS);
	}
}

async function hubspotContactCreate(data: any) {
	const hubspotContactCreateUrl =
		'https://api.hubapi.com/crm/v3/objects/contacts';

	const connection: any = await prisma.connection.findFirst({});

	if (!connection) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}
	const hapikey = connection?.hubSpotCredentials.hapikey;

	const authResponse = await axios.post(
		hubspotContactCreateUrl as string,
		data,
		{
			headers: {
				Authorization: `Bearer ${hapikey}`,
				'Content-Type': 'application/json',
			},
		}
	);
	if (authResponse.status === 201) {
		return {
			success: true,
		};
	}
}

async function hubspotReadContact(data: any) {
	const hubspotContactCreateUrl = `https://api.hubapi.com/crm/v3/objects/contacts/${data.contactId}`;

	const connection: any = await prisma.connection.findFirst({});

	if (!connection) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}
	const hapikey = connection?.hubSpotCredentials.hapikey;

	const authResponse = await axios.get(hubspotContactCreateUrl as string, {
		headers: {
			Authorization: `Bearer ${hapikey}`,
			'Content-Type': 'application/json',
		},
	});
	if (authResponse.status === 200) {
		return {
			success: true,
		};
	}
}

async function hubspotProductCreate(data: any) {
	const hubspotProductCreateUrl =
		'https://api.hubapi.com/crm/v3/objects/products';

	const connection: any = await prisma.connection.findFirst({});

	if (!connection) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}
	const hapikey = connection?.hubSpotCredentials.hapikey;

	const authResponse = await axios.post(
		hubspotProductCreateUrl as string,
		data,
		{
			headers: {
				Authorization: `Bearer ${hapikey}`,
				'Content-Type': 'application/json',
			},
		}
	);
	if (authResponse.status === 201) {
		return {
			success: true,
		};
	}
}

async function hubspotDealCreate(data: any) {
	const hubspotDealCreateUrl = 'https://api.hubapi.com/crm/v3/objects/deals';

	const connection: any = await prisma.connection.findFirst({});

	if (!connection) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}
	const hapikey = connection?.hubSpotCredentials.hapikey;

	const authResponse = await axios.post(hubspotDealCreateUrl as string, data, {
		headers: {
			Authorization: `Bearer ${hapikey}`,
			'Content-Type': 'application/json',
		},
	});
	if (authResponse.status === 201) {
		return {
			success: true,
		};
	}
}

async function hubspotQuotesCreate(data: any) {
	const hubspotQuotesCreateUrl = 'https://api.hubapi.com/crm/v3/objects/quotes';

	const connection: any = await prisma.connection.findFirst({});

	if (!connection) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}
	const hapikey = connection?.hubSpotCredentials.hapikey;

	const authResponse = await axios.post(
		hubspotQuotesCreateUrl as string,
		data,
		{
			headers: {
				Authorization: `Bearer ${hapikey}`,
				'Content-Type': 'application/json',
			},
		}
	);
	if (authResponse.status === 201) {
		return {
			success: true,
		};
	}
}

async function getConnection() {
	const connection: any = await prisma.connection.findFirst({});

	if (!connection) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}

	return {
		wooCommerceCredentials: connection.wooCommerceCredentials ? true : false,
		xeroCredentials: connection.xeroCredentials ? true : false,
		hubSpotCredentials: connection.hubSpotCredentials ? true : false,
		linnworksCredentials: connection.linnworksCredentials ? true : false,
		shopifyCredentials: connection.shopifyCredentials ? true : false,
		stripeCredentials: connection.stripeCredentials ? true : false,
		xeroCompanyName: connection.xeroCredentials?.organisation ? connection.xeroCredentials?.organisation : null,
		hubspotCompanyName: connection.hubSpotCredentials?.organisation ? connection.hubSpotCredentials?.organisation : null,
		wooCommerceCompanyName: connection.wooCommerceCredentials?.organisation ? connection.wooCommerceCredentials?.organisation : null,
		linnworksCompanyName: connection.linnworksCredentials?.organisation ? connection.linnworksCredentials?.organisation : null,
		shopifyCompanyName: connection.shopifyCredentials?.shop_name ? connection.shopifyCredentials?.shop_name : null,
		stripeCompanyName: connection.stripeCredentials?.organisation ? connection.stripeCredentials?.organisation : null,

	};
}

async function hubspotDisconnectService() {

	await prisma.connection.update({
		where: {
			id: process.env.CONNECTION_ID,
		},
		data: {
			hubSpotCredentials: null as any,
		},
	});
	return {
		success: true,
	};
}

async function wooCommerceDisconnectService() {

	await prisma.connection.update({
		where: {
			id: process.env.CONNECTION_ID,
		},
		data: {
			wooCommerceCredentials: null as any,
		},
	});
	return {
		success: true,
	};
}

async function xeroDisconnectService() {

	const connection: any = await prisma.connection.findFirst({});

	if (!connection?.xeroCredentials?.refresh_token) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}

	const clientId = process.env.XERO_CLIENT_ID;
	const clientSecret = process.env.XERO_CLIENT_SECRET;

	const authHeader = 'Basic ' + Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
	const params = new URLSearchParams();
	params.append('token', connection.xeroCredentials.refresh_token);

	try {
		await axios.post('https://identity.xero.com/connect/revocation', params, {
			headers: {
				'Authorization': authHeader,
				'Content-Type': 'application/x-www-form-urlencoded'
			}
		});

		await prisma.connection.update({
			where: { id: connection.id }, data: {
				xeroCredentials: null as any
			}
		})

		return {
			success: true,
		}

	} catch (error) {
		throw new ApiException(ErrorCodes.XERO_REVOKE_TOKEN);
	}
}

async function linnworksAuthService(data: any) {
	const linnworksUrl = process.env.LINNWORKS_AUTH_URL;

	const linnworksData = {
		ApplicationId: process.env.LINNWORKS_APPLICATION_ID,
		ApplicationSecret: process.env.LINNWORKS_APPLICATION_SECRET,
		Token: data.token
	}

	try {
		const authResponse = await axios.post(linnworksUrl as string, linnworksData, {
			headers: {
				'Content-Type': 'application/json',
			},
		});
		if (authResponse.data) {

			let organisation;
			if (authResponse.data.UserName) {
				organisation = authResponse.data.UserName
			}
			await prisma.connection.update({
				where: {
					id: process.env.CONNECTION_ID,
				},
				data: {
					linnworksCredentials: { ...linnworksData, organisation },
				},
			});
			return {
				success: true,
			};
		}
	} catch (error) {
		throw new ApiException(ErrorCodes.INVALID_LINNWORKS_CREDENTIALS);
	}
}

async function linnworksDisconnectService() {

	await prisma.connection.update({
		where: {
			id: process.env.CONNECTION_ID,
		},
		data: {
			linnworksCredentials: null as any,
		},
	});
	return {
		success: true,
	};
}

async function shopifyGetAuthUrlService(shopName: string) {
	const clientId = process.env.SHOPIFY_CLIENT_ID;
	const redirectUri = process.env.SHOPIFY_REDIRECT_URI;
	const scope = process.env.SHOPIFY_SCOPES;

	const authUrl = `https://${shopName}.myshopify.com/admin/oauth/authorize?client_id=${clientId}&scope=${scope}&redirect_uri=${redirectUri}`;

	return {
		authUrl,
	};
}

async function shopifyRedirectUrlService(data: any) {

	const clientId = process.env.SHOPIFY_CLIENT_ID;
	const clientSecret = process.env.SHOPIFY_CLIENT_SECRET;

	const { shop, code } = data
	try {
		const authResponse = await axios.post(
			`https://${shop}/admin/oauth/access_token`, {
			client_id: clientId,
			client_secret: clientSecret,
			code,
		});

		if (
			authResponse.data &&
			!invalidText(authResponse.data.access_token)
		) {
			const shopProfileResponse = await axios.get(`https://${shop}/admin/shop.json`, {
				headers: {
					'X-Shopify-Access-Token': authResponse.data.access_token,
				},
			});
			const responseData = {
				access_token: authResponse.data.access_token,
				organisation: shopProfileResponse.data.shop ? shopProfileResponse.data.shop.domain : null,
				shop_name: shopProfileResponse.data.shop ? shopProfileResponse.data.shop.name : null,

			};
			await prisma.connection.update({
				where: {
					id: process.env.CONNECTION_ID,
				},
				data: {
					shopifyCredentials: responseData,
				},
			});
			return {
				success: true,
			};
		}
	} catch (error) {
		throw new ApiException(ErrorCodes.INVALID_SHOPIFY_CALLBACK);
	}
}

async function shopifyDisconnectService() {

	const connection: any = await prisma.connection.findFirst({});

	if (!connection?.shopifyCredentials?.access_token) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}

	const response = await axios.delete(
		`https://${connection.shopifyCredentials.organisation}/admin/api_permissions/current.json`, {
		headers: {
			'X-Shopify-Access-Token': connection.shopifyCredentials.access_token,
			'Content-Type': 'application/json',
			accept: 'application/json'
		},
	});
	if (response.status === 200) {
		await prisma.connection.update({
			where: {
				id: process.env.CONNECTION_ID,
			},
			data: {
				shopifyCredentials: null as any,
			},
		});
		return {
			success: true,
		};
	}
}

async function stripeGetAuthUrlService() {
	const clientId = process.env.STRIPE_CLIENT_ID;
	const redirectUri = process.env.STRIPE_REDIRECT_URL;
	const scope = process.env.STRIPE_SCOPE;

	const authUrl = `https://connect.stripe.com/oauth/authorize?response_type=code&client_id=${clientId}&scope=${scope}&redirect_uri=${redirectUri}`;

	return {
		authUrl,
	};
}

async function stripeRedirectUrlService(data: any) {
	const clientSecret = process.env.STRIPE_TEST_KEY;

	try {
		const authResponse = await axios.post('https://connect.stripe.com/oauth/token', null, {
			params: {
				client_secret: clientSecret,
				code: data.code,
				grant_type: 'authorization_code',
			},
		});

		if (
			authResponse.data &&
			!invalidText(authResponse.data.access_token) &&
			!invalidText(authResponse.data.refresh_token)
		) {

			const response = await axios.get('https://api.stripe.com/v1/account', {
				headers: {
					Authorization: `Bearer ${clientSecret}`,
				},
			});

			const responseData = {
				access_token: authResponse?.data?.access_token,
				refresh_token: authResponse?.data?.refresh_token,
				organisation: response?.data?.settings?.dashboard?.display_name ? response.data.settings.dashboard.display_name : null,
				stripe_publishable_key: authResponse?.data?.stripe_publishable_key,
				stripe_user_id: authResponse?.data?.stripe_user_id,

			};

			await prisma.connection.update({
				where: {
					id: process.env.CONNECTION_ID,
				},
				data: {
					stripeCredentials: responseData,
				},
			});
			return {
				success: true,
			};
		}

	} catch (error) {
		throw new ApiException(ErrorCodes.INVALID_STRIPE_CALLBACK);
	}
}

async function stripeDisconnectService() {

	const connection: any = await prisma.connection.findFirst({});

	if (!connection?.stripeCredentials) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}

	const clientId = process.env.STRIPE_CLIENT_ID;
	const clientSecret = process.env.STRIPE_TEST_KEY;
	const stripeUserId = connection.stripeCredentials.stripe_user_id;

	const response = await axios.post('https://connect.stripe.com/oauth/deauthorize', null, {
		params: {
			client_id: clientId,
			client_secret: clientSecret,
			stripe_user_id: stripeUserId,
		},
	});

	if (response.status === 200) {

		await prisma.connection.update({
			where: {
				id: process.env.CONNECTION_ID,
			},
			data: {
				stripeCredentials: null as any,
			},
		});

		return {
			success: true,
		};
	}

}

export const integrationService = {
	woocommerceAuthService,
	xeroGetAuthUrlService,
	xeroRedirectUrlService,
	hubspotAuthService,
	hubspotContactCreate,
	hubspotReadContact,
	hubspotProductCreate,
	hubspotDealCreate,
	hubspotQuotesCreate,
	getConnection,
	hubspotDisconnectService,
	wooCommerceDisconnectService,
	xeroDisconnectService,
	linnworksAuthService,
	linnworksDisconnectService,
	shopifyGetAuthUrlService,
	shopifyRedirectUrlService,
	shopifyDisconnectService,
	stripeGetAuthUrlService,
	stripeRedirectUrlService,
	stripeDisconnectService
};
