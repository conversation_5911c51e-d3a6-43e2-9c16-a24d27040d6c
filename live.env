DATABASE_URL="postgresql://postgres:<EMAIL>:5432/rob-live?schema=public"

PORT = "8080"

ADMIN_EMAIL="<EMAIL>"

ADMIN_PASSWORD = "Satva1213#"

ACCESS_TOKEN_SECRET_KEY = "215e05837c66904a6762de428266d32dd7bf5865"

FORGOT_PASSWORD_TOKEN_SECRET_KEY = "321785c4cefc8da6b2f443f9dcd38cfb9064949a" 

VERIFICATION_TOKEN_SECRET_KEY = "279f37fd64be446db1b9e5e58297c1f57ad8d7e4" 

REACT_APP_BASE_URL = "https://app.crystalclear.co.uk"

SMTP_EMAIL= '<EMAIL>'
 
SMTP_EMAIL_LOGIN= '********************'

SMTP_PASSWORD= 'BLJ5TUys2e4XaiOyvuqj4bRnmx28hSBQ1QiMcipgRbPG'
 
SMTP_HOST= 'email-smtp.us-west-2.amazonaws.com'

SMTP_PORT= '587'

CONNECTION_ID = 'e6325969-7f55-4984-a65c-ccd35ea32936'

AWS_ACCESS_KEY = ********************

AWS_SECRET_KEY = l7orrmORa2s+lip7mfB4jXIuvArt9oph1KaGhukS

REGION = eu-west-2


XERO_CLIENT_ID = '406DD2DA51F0407AB20E135DFEE421C9'

XERO_CLIENT_SECRET = '-FjldyeCqZvnqEpA4EE_nnO4RW1RhrXYLgRpbyPWkdLar2ys'

XERO_REDIRECT_URI = 'https://app.crystalclear.co.uk/xero/callback'

XERO_STATE= '123'

XERO_SCOPE = 'openid profile email accounting.transactions offline_access accounting.settings accounting.attachments files accounting.contacts accounting.reports.read'




HUBSPOT_CLIENT_ID = "************************************" 

HUBSPOT_SCOPES = "oauth"

HUBSPOT_REDIRECT_URI = "https://app.crystalclear.co.uk/hubspot-callback"

HUBSPOT_API_KEY = "********************************************"

HUBSPOT_VERIFY_API_KEY = "https://api.hubapi.com/account-info/v3/api-usage/daily/private-apps"

HUBSPOT_DEAL_URL = "https://app.hubspot.com/contacts/********/record/0-3"

HUBSPOT_QUOTE_URL = "https://app.hubspot.com/quotes/********/edit"

HUBSPOT_CONTACT_URL = "https://app.hubspot.com/contacts/********/record/0-1"

HUBSPOT_INVOICE_URL = "https://app.hubspot.com/contacts/********/objects/0-53/views/all/list"




LINNWORKS_APPLICATION_ID = 'b2d8f413-1aa0-4e9a-a2c6-d0d1c9383856'

LINNWORKS_APPLICATION_SECRET = 'cf39b7b9-f7bb-485b-8847-413a071480d1'

LINNWORKS_AUTH_URL = 'https://api.linnworks.net/api/Auth/AuthorizeByApplication'

XERO_CONTACT_URL = 'https://go.xero.com/app/contacts/contact'

XERO_INVOICE_URL = 'https://go.xero.com/AccountsReceivable/View.aspx?InvoiceID' 

XERO_PAYMENT_URL = 'https://go.xero.com/Bank/ViewTransaction.aspx?bankTransactionID'

LINNWORKS_ORDER_URL = 'https://www.linnworks.net/#/app/ViewOrder/'

COGS_JOURNAL_URL = 'https://go.xero.com/Journal/View.aspx?invoiceID='

#shopify
SHOPIFY_CLIENT_ID="63ff42902c2512a23596533e4934c28e"

SHOPIFY_CLIENT_SECRET="3b1895e176db0d9651a2f16f14c7212e"

SHOPIFY_REDIRECT_URI ="https://app.crystalclear.co.uk/shopify/callback"

SHOPIFY_SCOPES = "read_orders,write_orders,read_products,write_products"


# Stripe

# STRIPE_CLIENT_ID = "ca_QX74T93ZVZKnCYlY5dCDbuFTm5m5JGFa"

# STRIPE_SCOPE ="read_write"

# STRIPE_TEST_KEY = "sk_test_51Pg2L2ED0iHLK0Vm4VmrNc4FTsYiO5tyrB3d3mnlkIEtFzY32W2wcMcOwoWNx9tl9bMgys1iy8jI9PmZsuHgPpo000AX9w4nJl"

# STRIPE_REDIRECT_URL = "https://app.crystalclear.co.uk/stripe/callback"



XERO_PRODUCT_QUEUE = 'https://sqs.eu-west-2.amazonaws.com/************/xero-products-queue'


SHOPIFY_ORDER_URL = "https://admin.shopify.com/store/"



XERO_BANK_TRANSACTION_DESCRIPTION = 'Received from stripe' 

XERO_BANK_ACCOUNT_NUMBER = '**********' 

XERO_DEFAULT_EMAIL = "<EMAIL>"

XERO_DEFAULT_NAME = "shopify sales"



# STRIPE_ENDPOINT_SECRET = 'whsec_AccTcdyLRbYcQFCjExYwrXzzmWzvTGIx'
# STRIPE_ENDPOINT_SECRET_FEE = 'whsec_ciatWeICtTj695eGpDjJBfZVwN9P0you'
# STRIPE_TEST_KEY = "sk_test_51Pg2L2ED0iHLK0Vm4VmrNc4FTsYiO5tyrB3d3mnlkIEtFzY32W2wcMcOwoWNx9tl9bMgys1iy8jI9PmZsuHgPpo000AX9w4nJl"



