// import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
// import moment from 'moment';
// import multerS3 from 'multer-s3';

// const s3accessKeyId = process.env.S3_ACCESS_KEY_ID;
// const s3secretAccessKey = process.env.S3_SECRET_ACCESS_KEY;

// export const s3Client = new S3Client({
// 	region: 'us-east-1',
// 	credentials: {
// 		accessKeyId: s3accessKeyId as string,
// 		secretAccessKey: s3secretAccessKey as string,
// 	},
// });

// export const s3Storage = multerS3({
// 	s3: s3Client,
// 	bucket: 'ees-commission',
// 	metadata: (req, file, cb) => {
// 		cb(null, { fieldname: file.fieldname });
// 	},
// 	key: (req, file, cb) => {
// 		const fileName =
// 			Date.now() + '_' + file.fieldname + '_' + file.originalname;
// 		cb(null, fileName);
// 	},
// });

// export const uploadToS3 = async (
// 	base64String: any,
// 	fileName: any,
// 	email: string
// ) => {
// 	// Convert base64 string to buffer
// 	const pdfBuffer = Buffer.from(base64String, 'base64');

// 	const uploadParams = {
// 		Bucket: 'ees-commission', // Replace with your bucket name
// 		Key: email + '/' + moment(new Date()).format('MM-DD-YYYY') + '/' + fileName,
// 		Body: pdfBuffer,
// 	};

// 	try {
// 		const data: any = await s3Client.send(new PutObjectCommand(uploadParams));
// 		return data; // Return the S3 file URL
// 	} catch (err) {
// 		console.error('Error uploading file to S3:', err);
// 		throw err; // Throw error for handling elsewhere
// 	}
// };
