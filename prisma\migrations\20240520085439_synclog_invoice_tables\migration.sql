-- CreateTable
CREATE TABLE "Synclog" (
    "id" TEXT NOT NULL,
    "connectionId" TEXT NOT NULL,
    "wooCommerceOrderId" TEXT,
    "hubspotContactId" TEXT,
    "hubspotProductId" TEXT,
    "hubspotDealId" TEXT,
    "hubspotQuoteId" TEXT,
    "hubspotInvoiceId" TEXT,
    "linnworksOrderId" TEXT,
    "xeroInvoiceId" TEXT,
    "hubspotPaymentId" TEXT,
    "xeroPaymentId" TEXT,
    "type" TEXT,
    "syncStatus" TEXT,
    "processingError" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Synclog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LastSyncDate" (
    "id" TEXT NOT NULL,
    "connectionId" TEXT NOT NULL,
    "moduleType" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LastSyncDate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Invoices" (
    "id" TEXT NOT NULL,
    "wooCommerceCustomer" JSONB,
    "wooCommerceOrderId" TEXT,
    "hubSpotInvoiceId" TEXT,
    "linnworksOrderId" TEXT,
    "xeroInvoiceId" TEXT,
    "wooCommerceOrderNumber" TEXT,
    "wooCommerceAmount" TEXT,
    "wooCommerceOrderDate" TIMESTAMP(3),
    "hubSpotPaymentStatus" BOOLEAN,
    "hubSpotPaymentId" TEXT,
    "xeroPaymentId" TEXT,
    "postalTrackingNumber" TEXT,
    "trackingUrl" TEXT,
    "syncLogId" TEXT NOT NULL,
    "synclogId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Invoices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceLines" (
    "id" TEXT NOT NULL,
    "lineId" TEXT,
    "invoiceId" TEXT NOT NULL,
    "description" TEXT,
    "quantity" INTEGER,
    "unitPrice" DOUBLE PRECISION,
    "totalPrice" DOUBLE PRECISION,
    "sku" TEXT,
    "productName" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InvoiceLines_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Synclog_id_key" ON "Synclog"("id");

-- CreateIndex
CREATE UNIQUE INDEX "LastSyncDate_id_key" ON "LastSyncDate"("id");

-- CreateIndex
CREATE UNIQUE INDEX "Invoices_id_key" ON "Invoices"("id");

-- CreateIndex
CREATE UNIQUE INDEX "Invoices_synclogId_key" ON "Invoices"("synclogId");

-- CreateIndex
CREATE UNIQUE INDEX "InvoiceLines_id_key" ON "InvoiceLines"("id");

-- AddForeignKey
ALTER TABLE "Synclog" ADD CONSTRAINT "Synclog_connectionId_fkey" FOREIGN KEY ("connectionId") REFERENCES "Connection"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LastSyncDate" ADD CONSTRAINT "LastSyncDate_connectionId_fkey" FOREIGN KEY ("connectionId") REFERENCES "Connection"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Invoices" ADD CONSTRAINT "Invoices_synclogId_fkey" FOREIGN KEY ("synclogId") REFERENCES "Synclog"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoiceLines" ADD CONSTRAINT "InvoiceLines_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES "Invoices"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
