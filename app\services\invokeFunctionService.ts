import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';
import { awsConfig } from '../client/aws';
import axios from 'axios';
import { Request } from 'express';

const lambda = new LambdaClient(awsConfig);
async function invokeWoocommerceOrder() {
	const params = new InvokeCommand({
		FunctionName: 'woocommerce-sales-order-download-dev-SalesOrderDownload',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	const getQuoteParams = new InvokeCommand({
		FunctionName: 'quote-creation-hubspot-dev-quoteCreation',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	lambda.send(params);
	await lambda.send(getQuoteParams);
	return {
		success: true,
	};
}

async function invokeWoocommerceSalesOrder(req: Request) {
	const authHeader = req.headers.authorization;

	if (authHeader && authHeader.startsWith('Basic ')) {
		const base64Credentials = authHeader.slice(6);
		const credentials = Buffer.from(base64Credentials, 'base64').toString(
			'ascii'
		);
		const [username, password] = credentials.split(':');
		try {
			const response = await axios.get(
				`https://satvasolutions.com/CrystalClearStaging/wp-json/wc/v3/orders`,
				{
					auth: {
						username,
						password,
					},
					// Forward any query parameters from the original request
					params: req.query,
				}
			);
			return response.data;
		} catch (error: any) {
			if (error.response) {
				throw new Error(
					`WooCommerce API error: ${error.response.status} - ${JSON.stringify(
						error.response.data
					)}`
				);
			} else if (error.request) {
				throw new Error('No response received from WooCommerce API');
			} else {
				throw new Error(`Error making request: ${error.message}`);
			}
		}
	}
	throw new Error('No authorization header provided');
}

async function invokeHubspotInvoices() {
	const params = new InvokeCommand({
		FunctionName: 'hubspot-invoice-download-dev-handler',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	await lambda.send(params);
	return {
		success: true,
	};
}

async function invokeHubspotPayment() {
	const params = new InvokeCommand({
		FunctionName: 'hubspot-paid-invoice-download-dev-handler',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	await lambda.send(params);
	return {
		success: true,
	};
}

async function invokeJournal() {
	const params = new InvokeCommand({
		FunctionName: 'linnworks-cogs-dev-handler',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	await lambda.send(params);
	return {
		success: true,
	};
}

async function invokeLinnworksOrderTracking() {
	const params = new InvokeCommand({
		FunctionName: 'linnworks-order-tracking-dev-handler',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	await lambda.send(params);
	return {
		success: true,
	};
}

async function invokeSyncStock() {
	const params = new InvokeCommand({
		FunctionName: 'linnworks-hubspot-stock-update-dev-handler',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	await lambda.send(params);
	return {
		success: true,
	};
}

async function invokeShopifyGetOrders() {
	const params = new InvokeCommand({
		FunctionName: 'shopify-get-orders-service-dev-handler',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	await lambda.send(params);
	return {
		success: true,
	};
}
async function invokeShopifyGetProducts() {
	const params = new InvokeCommand({
		FunctionName: 'shopify-get-product-service-dev-handler',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	await lambda.send(params);
	return {
		success: true,
	};
}

async function invokeXeroAccounts() {
	const params = new InvokeCommand({
		FunctionName: 'xero-account-download-dev-handler',
		InvocationType: 'Event', // or 'Event' for asynchronous
	});
	await lambda.send(params);
	return {
		success: true,
	};
}

export const invokeFunctionService = {
	invokeWoocommerceOrder,
	invokeHubspotInvoices,
	invokeHubspotPayment,
	invokeJournal,
	invokeLinnworksOrderTracking,
	invokeSyncStock,
	invokeShopifyGetOrders,
	invokeShopifyGetProducts,
	invokeXeroAccounts,
	invokeWoocommerceSalesOrder,
};
