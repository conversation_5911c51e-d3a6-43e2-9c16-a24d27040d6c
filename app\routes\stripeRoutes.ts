import express, { Request, Response } from 'express';
import { stripeService } from '../services/stripeHandler';
import asyncHandler from '../utils/async-handler';

const router = express.Router();

router.post(
    '/payout',
    express.raw({ type: 'application/json' }),
    asyncHandler(async (req: Request, res: Response) => {
        return stripeService.stripePayoutHandler(req, res);
    })
);

router.post(
    '/fees',
    express.raw({ type: 'application/json' }),
    asyncHandler(async (req: Request, res: Response) => {
        return stripeService.stripeFeeTrasaction(req, res);
    })
);


export default router;
