import { body } from 'express-validator';

// Login validation rules
export const loginValidationRules = [
	body('email')
		.trim()
		.notEmpty()
		.withMessage('Email is required')
		.isEmail()
		.withMessage('Please enter valid email'),
	body('password').trim().notEmpty().withMessage('Password is required'),
];

export const forgotPasswordValidationRules = [
	body('email').trim().notEmpty().isEmail().withMessage('Invalid email address'),
];

// Change Password validation rules
export const changePasswordValidationRules = [
	body('password')
		.isLength({ min: 8 })
		.withMessage('Password must be at least 8 characters long')
		.matches(
			/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+])[A-Za-z\d!@#$%^&*()_+]{8,16}$/
		)
		.withMessage(
			'Password should have minimum 8 and maximum 16 characters with at least 1 uppercase, 1 lowercase, 1 number and 1 special character.'
		),

	body('confirmPassword')
		.notEmpty()
		.withMessage('Confirm password required')
		.custom((value: any, { req }: any) => {
			if (value !== req.body.password) {
				throw new Error('Passwords do not match');
			}
			return true;
		}),
];

// Reset Password validation rules
export const resetPasswordValidationRules = [
	body('password')
		.trim()
		.isLength({ min: 8 })
		.withMessage('Password must be at least 8 characters long')
		.matches(
			/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+])[A-Za-z\d!@#$%^&*()_+]{8,16}$/
		)
		.withMessage(
			'Password should have minimum 8 and maximum 16 characters with at least 1 uppercase, 1 lowercase, 1 number and 1 special character.'
		),

	body('confirmPassword')
		.trim()
		.notEmpty()
		.withMessage('Confirm password required')
		.custom((value: any, { req }: any) => {
			if (value !== req.body.password) {
				throw new Error('Passwords do not match with confirm password');
			}
			return true;
		}),

	body('token').notEmpty().withMessage('Token is required'),
];

// Update profile validation rules

export const updateProfileValidationRules = [
	body('firstName')
		.optional()
		.isLength({ min: 2, max: 50 })
		.withMessage(
			'First name length must be minimum 2 characters and maximum 50 characters.'
		),
	body('lastName')
		.optional()
		.isLength({ min: 2, max: 50 })
		.withMessage(
			'Last name length must be minimum 2 characters and maximum 50 characters.'
		),
	// body('phone')
	// 	.optional()
	// 	.matches(/^\d{10}$/)
	// 	.withMessage('Invalid phone number format'),
];

export function isValidUUID(uuid: string) {
	const uuidRegex =
		/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
	return uuidRegex.test(uuid);
}

export const inviteUserValidationRules = [
	body('email')
		.trim()
		.notEmpty()
		.withMessage('Email is required')
		.isEmail()
		.withMessage('Please enter valid email address'),
	body('name').trim().notEmpty().withMessage('Name is required'),
	body('roleId').notEmpty().withMessage('Role Id is required'),
];

export const updateUserValidationRules = [
	body('name').trim().notEmpty().withMessage('Name is required'),
	body('roleId').trim().notEmpty().withMessage('Role Id is required'),
];

export const woocommerceCredentialRules = [
	body('baseurl').trim().notEmpty().withMessage('baseurl is required'),
	body('consumer_key').trim().notEmpty().withMessage('Consumer key is required'),
	body('consumer_secret').trim().notEmpty().withMessage('Consumer secret is required'),
];

export const hubspotAuthRules = [
	body('hapikey').trim().notEmpty().withMessage('hapikey is required'),
];

export const xeroCallbackRules = [
	body('url').notEmpty().withMessage('url is required'),
];

export const reinviteUserValidationRules = [
	body('email')
		.notEmpty()
		.withMessage('Email is required')
		.isEmail()
		.withMessage('Please enter valid email address'),
];

export const linnworksAuthRules = [
	body('token').notEmpty().withMessage('token is required'),
];

export const stripeCallbackRules = [
	body('code').notEmpty().withMessage('code is required'),
];

export const updateProductRules = [
	body('*.salesPrice').trim().notEmpty().withMessage('sales price is required'),
	body('*.salesCOA').trim().notEmpty().withMessage('sales COA is required'),
	body('*.purchasePrice').trim().notEmpty().withMessage('purchase price is required'),
	body('*.purchaseCOA').trim().notEmpty().withMessage('purchase COA is required'),
	body('*.tax').trim().notEmpty().withMessage('tax is required'),
];

export const updatePaymentGatewayRules = [
	body('*.name').trim().notEmpty().withMessage('Payment Gateway name is required'),
	body('*.coaAccount').trim().notEmpty().withMessage('Coa Account is required'),
	body('*.feesAccount').trim().notEmpty().withMessage('Fees Account is required'),
];