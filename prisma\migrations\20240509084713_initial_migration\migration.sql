-- CreateTable
CREATE TABLE "MigrationLock" (
    "id" TEXT NOT NULL,
    "isLocked" BOOLEAN NOT NULL,
    "lockedBy" TEXT NOT NULL,
    "uid" TEXT NOT NULL,
    "gid" TEXT NOT NULL,
    "env" TEXT NOT NULL,
    "os" TEXT NOT NULL,

    CONSTRAINT "MigrationLock_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Migration" (
    "id" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "uid" TEXT NOT NULL,
    "gid" TEXT NOT NULL,
    "env" TEXT NOT NULL,
    "os" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "Migration_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "MigrationLock_id_key" ON "MigrationLock"("id");

-- CreateIndex
CREATE UNIQUE INDEX "Migration_id_key" ON "Migration"("id");
