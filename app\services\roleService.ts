import { prisma } from '../client/prisma'
import { ILoggedInUser } from '../interfaces/global';
import { RoleTypes } from '../utils/constants';
import ApiException from '../utils/errorHandler';
import { ErrorCodes } from '../utils/response';

async function getAllRoles() {
    return prisma.role.findMany({
        orderBy: {
            id: 'asc'
        }
    });
}

async function changeRoleStatus(id: string, roleStatus: string, user: ILoggedInUser) {

    const roleData = await prisma.role.findFirst({
        where: {
            id
        }
    })

    if (!roleData) {
        throw new ApiException(ErrorCodes.ROLE_NOT_FOUND)
    }

    if (roleData.type === RoleTypes.SUPER_ADMIN) {
        throw new ApiException(ErrorCodes.CAN_NOT_EDIT_ROLE)
    }

    await prisma.role.update({
        where: {
            id
        },
        data: {
            status: roleStatus === 'Active',
            updatedBy: user.id
        }
    });

    return {
        success: true
    }
}

export const roleService = {
    getAllRoles,
    changeRoleStatus
}