import express from 'express';
import { IQuery, RequestExtended } from '../interfaces/global';
import { synclogService } from '../services/synclogService';
import asyncHandler from '../utils/async-handler';
import { syncHubSpotInvoicesToXero } from '../services/syncHubspotXero';

const router = express.Router();

router.get(
	'/',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getSynclogs(req.query as IQuery);
	})
);

router.get(
	'/:id',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getSyncLogHistoryById(req.params.id);
	})
);

router.get(
	'/cogs',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getCOGSSynclogs(req.query as IQuery);
	})
);

router.get(
	'/b2c/synclogs',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getB2CSynclogs(req.query as IQuery);
	})
);

router.get(
	'/b2c/:id',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getB2CSyncLogHistoryById(req.params.id);
	})
);

router.get(
	'/shopify/orders',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getOrdersByPaymentGatewayAndDate(req.query as IQuery);
	})
);

router.get(
	'/invoices/hubspot-xero',
	asyncHandler(async (req: RequestExtended) => {
		return syncHubSpotInvoicesToXero(
			req.query as {
				fromDate?: string;
				toDate?: string;
				page?: number;
			},
		);
	})
);

export default router;
