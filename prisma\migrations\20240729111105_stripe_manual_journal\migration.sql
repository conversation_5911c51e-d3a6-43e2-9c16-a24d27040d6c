-- CreateTable
CREATE TABLE "stripeJournalEntry" (
    "id" TEXT NOT NULL,
    "stripeAmount" TEXT NOT NULL,
    "xeroJournalId" TEXT NOT NULL,
    "journalNo" TEXT NOT NULL,
    "journalMonth" TEXT NOT NULL,
    "journalDate" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "stripeJournalEntry_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "stripeJournalEntry_id_key" ON "stripeJournalEntry"("id");

-- CreateIndex
CREATE UNIQUE INDEX "stripeJournalEntry_journalNo_key" ON "stripeJournalEntry"("journalNo");
