import express, { Request } from 'express';
import asyncHandler from '../utils/async-handler';

const router = express.Router();

router.get(
    '/lambda/hubspot',
    asyncHandler(async (req: Request) => {

        const query = req.query;

        (global as any).io.emit('synced-with-hubspot', `successfully synced woo-commerce order #: ${query.id}`);

        return {
            success: true
        };
    })
);
router.get(
    '/lambda/customerlog/hubspot',
    asyncHandler(async (req: Request) => {

        const query = req.query;

        (global as any).io.emit('synced-with-success-hubspot', `successfully synced hubspot quote #: ${query.id}`);

        return {
            success: true
        };
    })
);

router.get(
    '/lambda/linnworks',
    asyncHandler(async (req: Request) => {

        const query = req.query;

        (global as any).io.emit('synced-with-linnworks', `successfully synced shopify order #: ${query.id}`);

        return {
            success: true
        };
    })
);

router.get(
    '/xero/accounts',
    asyncHandler(async () => {

        (global as any).io.emit('synced-with-xero-accounts', `successfully synced xero accounts`);

        return {
            success: true
        };
    })
);

router.get(
    '/shopify/products',
    asyncHandler(async (req: Request) => {

        const query = req.query;

        (global as any).io.emit('synced-with-shopify-products', `successfully synced shopify products #: ${query.id}`);

        return {
            success: true
        };
    })
);



export default router;
