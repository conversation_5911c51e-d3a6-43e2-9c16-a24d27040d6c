import { IUserReInviteData } from './../interfaces/userInterface';
import { prisma } from '../client/prisma';
import sendEmail from '../helpers/emailHelper';
import { generateForgotPasswordToken } from '../helpers/tokenHelper';
import { ILoggedInUser, IQuery } from '../interfaces/global';
import { IUserInviteData, IUserUpdateData } from '../interfaces/userInterface';
import { getInvitationEmailTemplate } from '../templates/email/inviteSalesRepsTemplate';
import { RoleTypes } from '../utils/constants';
import ApiException from '../utils/errorHandler';
import { ErrorCodes } from '../utils/response';
import { getSkipCount, hasText } from '../utils/utils';

async function inviteUser(data: IUserInviteData, user: ILoggedInUser) {

    const findExistingUser = await prisma.users.findFirst({
        where: {
            email: data.email.trim(),
            isDeleted: false
        }
    });

    if (findExistingUser) {
        throw new ApiException(ErrorCodes.USER_ALREADY_EXISTS)
    }

    const roleData = await prisma.role.findFirst({
        where: {
            id: data.roleId
        }
    })

    if (!roleData?.status) {
        throw new ApiException(ErrorCodes.ROLE_NOT_FOUND);
    }

    if (roleData.type === RoleTypes.SUPER_ADMIN) {
        throw new ApiException(ErrorCodes.CAN_NOT_ASSIGN_ROLE);
    }

    const createUser = await prisma.users.create({
			data: {
				email: data.email,
				name: data.name.trim(),
				roleId: data.roleId,
				createdBy: user.id,
				updatedBy: user.id,
			},
		});

    const forgotPassToken = generateForgotPasswordToken(
			{
				email: data.email.trim(),
				userId: createUser.id,
			},
			7 * 60 * 60
		);

    const emailTemplate = getInvitationEmailTemplate({
        fullName: createUser.name,
        url: `${process.env.REACT_APP_BASE_URL}/reset-password?token=${forgotPassToken}`
    })

    await prisma.users.update({
        where: {
            id: createUser.id
        },
        data: {
            forgotPasswordToken: forgotPassToken
        }
    })

    await sendEmail({
        from: `${process.env.SMTP_EMAIL}`,
        to: createUser.email,
        subject: 'Welcome - Crystal Clear',
        html: emailTemplate,
    })

    return {
        success: true
    }
}

async function editUser(data: IUserUpdateData, user: ILoggedInUser) {

    const findUser = await prisma.users.findFirst({
        where: {
            id: data.userId
        }
    })

    if (!findUser) {
        throw new ApiException(ErrorCodes.USER_NOT_FOUND);
    }

    const roleData = await prisma.role.findFirst({
        where: {
            id: data.roleId
        }
    })

    if (!roleData?.status) {
        throw new ApiException(ErrorCodes.ROLE_NOT_FOUND);
    }

    if (roleData.type === RoleTypes.SUPER_ADMIN) {
        throw new ApiException(ErrorCodes.CAN_NOT_ASSIGN_ROLE);
    }

    await prisma.users.update({
        where: {
            id: data.userId
        },
        data: {
            name: data.name.trim(),
            roleId: data.roleId,
            updatedBy: user.id
        }
    })

    return {
        success: true
    }
}

async function getAllUsers(query: IQuery) {

    const _query: any = {};

    const sort: any[] = []

    if (query.sortBy && query.sortOrder) {
        sort.push({
            [query.sortBy]: query.sortOrder
        })
    }

    sort.push({
        id: 'desc'
    })
    const orQuey: any[] = [];

    if (hasText(query.text)) {
        orQuey.push(
            {
                name: {
                    contains: query.text,
                    mode: 'insensitive'
                }
            },
            {
                email: {
                    contains: query.text,
                    mode: 'insensitive'
                }
            }
        )
    }

    if (orQuey.length) {
        _query.OR = orQuey
    }

    const users = await prisma.users.findMany({
        where: { ..._query, isDeleted: false },
        orderBy: sort,
        skip: getSkipCount(query.pageNo, query.pageSize),
        take: Number(query.pageSize) || 10,
        include: {
            role: true
        }
    })

    const count = await prisma.users.count({
        where: { ..._query, isDeleted: false },
    })

    const content = users.map((e) => {
        return {
            id: e.id,
            email: e.email,
            name: e.name,
            role: e.role.name,
            status: e.status,
            roleType: e.role.type,
            roleId: e.role.id
        }
    })

    return {
        content,
        count
    }
}

async function changeUserStatus(id: string, userStatus: string, user: ILoggedInUser) {

    const userData: any = await prisma.users.findFirst({
        where: {
            id
        },
        include: {
            role: true,
        }
    })

    if (!userData) {
        throw new ApiException(ErrorCodes.USER_NOT_FOUND)
    }

    if (userData.role.type === RoleTypes.SUPER_ADMIN) {
        throw new ApiException(ErrorCodes.CAN_NOT_EDIT_USER)
    }

    await prisma.users.update({
        where: {
            id
        },
        data: {
            status: userStatus === 'Active',
            updatedBy: user.id
        }
    });

    return {
        success: true
    }
}

async function deleteUser(id: string) {

    const userData: any = await prisma.users.findFirst({
        where: {
            id
        },
        include: {
            role: true,
        }
    })

    if (!userData) {
        throw new ApiException(ErrorCodes.USER_NOT_FOUND)
    }

    if (userData.role.type === RoleTypes.SUPER_ADMIN) {
        throw new ApiException(ErrorCodes.CAN_NOT_DELETE_USER)
    }

    await prisma.users.update({
        where: {
            id
        },
        data: {
            isDeleted: true,
        }
    });

    return {
        success: true
    }
}

async function reinviteUser(data: IUserReInviteData) {

    const findExistingUser = await prisma.users.findFirst({
        where: {
            email: data.email
        },
        include: {
            role: true,
        }
    });

    if (!findExistingUser) {
        throw new ApiException(ErrorCodes.USER_NOT_FOUND)
    }

    if (findExistingUser.role.type === RoleTypes.SUPER_ADMIN) {
        throw new ApiException(ErrorCodes.CAN_NOT_REINVITE_USER);
    }


    const forgotPassToken = generateForgotPasswordToken({
        email: data.email,
        userId: findExistingUser.id
    }, 7 * 60 * 60);

    const emailTemplate = getInvitationEmailTemplate({
        fullName: findExistingUser.name,
        url: `${process.env.REACT_APP_BASE_URL}/reset-password?token=${forgotPassToken}`
    })

    await prisma.users.update({
        where: {
            id: findExistingUser.id
        },
        data: {
            forgotPasswordToken: forgotPassToken
        }
    })

    await sendEmail({
        from: `${process.env.SMTP_EMAIL}`,
        to: findExistingUser.email,
        subject: 'Welcome - Crystal Clear',
        html: emailTemplate,
    })

    return {
        success: true
    }

}


export const userService = {
    getAllUsers,
    inviteUser,
    editUser,
    changeUserStatus,
    deleteUser,
    reinviteUser
}