import express from 'express';
import { RequestExtended } from '../interfaces/global';
import asyncHandler from '../utils/async-handler';
import { hubspotAuthRules, linnworksAuthRules, stripeCallbackRules, woocommerceCredentialRules, xeroCallbackRules } from '../helpers/validators';
import { isAdmin } from '../middlewares/authMiddleware';
import { integrationService } from '../services/integrationService';

const router = express.Router();

router.post(
    '/woocommerce/auth',
    isAdmin,
    woocommerceCredentialRules,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.woocommerceAuthService(req.body);
    })
);

router.get(
    '/xero/auth',
    isAdmin,
    asyncHandler(async () => {
        return integrationService.xeroGetAuthUrlService();
    })
);

router.post(
    '/xero/callback',
    isAdmin,
    xeroCallbackRules,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.xeroRedirectUrlService(req.body);
    })
);

router.post(
    '/hubspot/auth',
    isAdmin,
    hubspotAuthRules,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.hubspotAuthService(req.body);
    })
);

router.post(
    '/hubspot/contact/create',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.hubspotContactCreate(req.body);
    })
);

router.get(
    '/hubspot/contact/read',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.hubspotReadContact(req.query);
    })
);

router.post(
    '/hubspot/product',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.hubspotProductCreate(req.body);
    })
);

router.post(
    '/hubspot/deal',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.hubspotDealCreate(req.body);
    })
);

router.post(
    '/hubspot/quote',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.hubspotQuotesCreate(req.body);
    })
);

router.get(
    '/',
    isAdmin,
    asyncHandler(async () => {
        return integrationService.getConnection();
    })
);

router.post(
    '/hubspot/disconnect',
    isAdmin,
    asyncHandler(async () => {
        return integrationService.hubspotDisconnectService();
    })
);

router.post(
    '/woocommerce/disconnect',
    isAdmin,
    asyncHandler(async () => {
        return integrationService.wooCommerceDisconnectService();
    })
);

router.post(
    '/xero/disconnect',
    isAdmin,
    asyncHandler(async () => {
        return integrationService.xeroDisconnectService();
    })
);

router.post(
    '/linnworks/auth',
    isAdmin,
    linnworksAuthRules,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.linnworksAuthService(req.body);
    })
);

router.post(
    '/linnworks/disconnect',
    isAdmin,
    asyncHandler(async () => {
        return integrationService.linnworksDisconnectService();
    })
);

router.get(
    '/shopify/auth',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        const { shopName } = req.query
        return integrationService.shopifyGetAuthUrlService(shopName as string);
    })
);


router.post(
    '/shopify/callback',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.shopifyRedirectUrlService(req.body);
    })
);

router.post(
    '/shopify/disconnect',
    isAdmin,
    asyncHandler(async () => {
        return integrationService.shopifyDisconnectService();
    })
);

router.get(
    '/stripe/auth',
    isAdmin,
    asyncHandler(async () => {
        return integrationService.stripeGetAuthUrlService();
    })
);

router.post(
    '/stripe/callback',
    isAdmin,
    stripeCallbackRules,
    asyncHandler(async (req: RequestExtended) => {
        return integrationService.stripeRedirectUrlService(req.body);
    })
);

router.post(
    '/stripe/disconnect',
    isAdmin,
    asyncHandler(async () => {
        return integrationService.stripeDisconnectService();
    })
);


export default router;
