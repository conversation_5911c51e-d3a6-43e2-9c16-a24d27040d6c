import express from 'express';
import {
    forgotPasswordValidationRules,
    loginValidationRules,
    resetPasswordValidationRules,
} from '../helpers/validators';
import asyncHandler from '../utils/async-handler';
import { authService } from '../services/authService';
import { RequestExtended } from '../interfaces/global';
import { isAuthenticated } from '../middlewares/authMiddleware';

const router = express.Router();

router.post(
    '/login',
    loginValidationRules,
    asyncHandler(async (req: RequestExtended) => {
        return authService.login(req.body);
    })
);

router.post(
    '/forgot-password',
    forgotPasswordValidationRules,
    asyncHandler(async (req: RequestExtended) => {
        return authService.generateForgotPassLink(req.body);
    })
);

router.post(
    '/reset-password',
    resetPasswordValidationRules,
    asyncHandler(async (req: RequestExtended) => {
        return authService.resetPassword(req.body);
    })
);

router.post(
    '/logout',
    isAuthenticated,
    asyncHandler(async (req: RequestExtended) => {
        return authService.logout(req);
    })
);

router.get(
    '/fetch-profile',
    isAuthenticated,
    asyncHandler(async (req: RequestExtended) => {
        return authService.fetchProfile(req.user);
    })
);

router.put(
    '/profile',
    isAuthenticated,
    asyncHandler(async (req: RequestExtended) => {
        return authService.updateProfile(req.body, req.user);
    })
);

export default router;
