import express from 'express';
import asyncHandler from '../utils/async-handler';
import { isAdmin } from '../middlewares/authMiddleware';
import { IQuery, RequestExtended } from '../interfaces/global';
import { userService } from '../services/userService';
import { inviteUserValidationRules, reinviteUserValidationRules, updateUserValidationRules } from '../helpers/validators';

const router = express.Router();

router.get(
    '/',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        return userService.getAllUsers(req.query as IQuery);
    })
);

router.post(
    '/invite',
    isAdmin,
    inviteUserValidationRules,
    asyncHandler(async (req: RequestExtended) => {
        return userService.inviteUser(req.body, req.user);
    })
);

router.put(
    '/:id',
    isAdmin,
    updateUserValidationRules,
    asyncHandler(async (req: RequestExtended) => {
        req.body.userId = req.params.id
        return userService.editUser(req.body, req.user);
    })
);

router.put(
    '/:id/status/:status',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        return userService.changeUserStatus(req.params.id, req.params.status, req.user);
    })
);

router.put(
    '/delete/:id',
    isAdmin,
    asyncHandler(async (req: RequestExtended) => {
        req.body.userId = req.params.id
        return userService.deleteUser(req.body.userId);
    })
);

router.post(
    '/reinvite',
    isAdmin,
    reinviteUserValidationRules,
    asyncHandler(async (req: RequestExtended) => {
        return userService.reinviteUser(req.body);
    })
);
export default router;
