import express from 'express';
import { dashboardService } from '../services/dashboardService';
import asyncHandler from '../utils/async-handler';
import { RequestExtended } from '../interfaces/global';

const router = express.Router();

router.get(
    '/product',
    asyncHandler(async (req: RequestExtended) => {
        return dashboardService.getProducts(req.query);
    })
);

router.get(
    '/sales',
    asyncHandler(async () => {
        return dashboardService.getSales();
    })
);

router.get(
	'/salesCounts',
	asyncHandler(async (req: RequestExtended) => {
		return dashboardService.getSalesCount(req.query);
	})
);

router.get(
	'/orderCounts',
	asyncHandler(async (req: RequestExtended) => {
		return dashboardService.getOrderAndSalesCount(req.query);
	})
);

router.get(
	'/shopifySales',
	asyncHandler(async () => {
		return dashboardService.getShopifySales();
	})
);
export default router;
