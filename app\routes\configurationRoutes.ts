import express from 'express';
import asyncHandler from '../utils/async-handler';
import { IQuery, RequestExtended } from '../interfaces/global';
import { isAdmin } from '../middlewares/authMiddleware';
import { configurationService } from '../services/configurationService';
import { updatePaymentGatewayRules, updateProductRules } from '../helpers/validators';

const router = express.Router();

router.get(
	'/shopifyProducts',
	isAdmin,
	asyncHandler(async (req: RequestExtended) => {
		return configurationService.getProducts(req.query as IQuery);
	})
);

router.put(
	'/shopify/products',
	isAdmin,
	updateProductRules,
	asyncHandler(async (req: RequestExtended) => {
		return configurationService.updateProducts(req.body);
	})
);

router.get(
	'/xero/accounts',
	isAdmin,
	asyncHandler(async () => {
		return configurationService.getXeroAccounts();
	})
);

router.get(
	'/xero/taxType',
	isAdmin,
	asyncHandler(async () => {
		return configurationService.getTaxRates();
	})
);

router.get(
	'/paymentGateways',
	isAdmin,
	asyncHandler(async (req: RequestExtended) => {
		return configurationService.getPaymentGateway(req.query as IQuery);
	})
);

router.put(
	'/paymentGateways/update',
	isAdmin,
	updatePaymentGatewayRules,
	asyncHandler(async (req: RequestExtended) => {
		return configurationService.updatePaymentGateway(req.body);
	})
);

export default router;
