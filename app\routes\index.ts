import express from 'express';
import asyncHandler from '../utils/async-handler';
import { customError, notFound } from '../utils/errorHandler';
import authRoutes from './authRoutes';
import roleRoutes from './roleRoutes';
import userRoutes from './userRoutes';
import connectionRoutes from './connectionRoutes';
import invokeFunctionRoutes from './invokeFunction';
import synclogRoutes from './synclogRoutes';
import callbackRoutes from './callbackRoutes';
import dashboardRoutes from './dashboardRoutes';
import { isAuthenticated } from '../middlewares/authMiddleware';
import configurationRoutes from './configurationRoutes';
import stripeRoutes from './stripeRoutes';
import woocommerceRoutes from './woocommerceRoutes';

const router = express.Router();

router.use(
	'/test',
	asyncHandler(async () => {
		return {
			message: 'Hello from Acton payroll',
		};
	})
);
router.use('/auth', authRoutes);
router.use('/roles', isAuthenticated, roleRoutes);
router.use('/users', isAuthenticated, userRoutes);
router.use('/connection', isAuthenticated, connectionRoutes);
router.use('/invoke', isAuthenticated, invokeFunctionRoutes);
router.use('/synclogs', isAuthenticated, synclogRoutes);
router.use('/callback', callbackRoutes);
router.use('/dashboard', isAuthenticated, dashboardRoutes);
router.use('/configuration', isAuthenticated, configurationRoutes);
router.use('/stripe', stripeRoutes);
router.use('/woocommerce', woocommerceRoutes);

router.use(notFound);
router.use(customError);

export default router;
