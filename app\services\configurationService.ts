import moment from 'moment';
import { prisma } from '../client/prisma';
import { IQuery } from '../interfaces/global';
import { getSkipCount, hasText } from '../utils/utils';
import axios from 'axios';
import ApiException from '../utils/errorHandler';
import { ErrorCodes } from '../utils/response';
import { sendMessageToXeroProductQueue } from '../client/sqs';
import { Prisma } from '@prisma/client';

interface DateRange {
	gte?: string;
	lt?: string;
}

interface WhereClause {
	createdAt?: DateRange;
	title?: Prisma.StringNullableFilter;
	[key: string]: string | DateRange | Prisma.StringNullableFilter | undefined;
}

class InvalidDateError extends Error {
	constructor(message: string) {
		super(message);
		this.name = 'InvalidDateError';
	}
}

function createDateRange(fromDate?: string, toDate?: string) {
	let dateRange: { gte?: string; lt?: string } = {};

	if (hasText(fromDate)) {
		dateRange['gte'] = moment(fromDate).startOf('day').toISOString();
	}
	if (hasText(toDate)) {
		dateRange['lt'] = moment(toDate).endOf('day').toISOString();
	}
	return dateRange;
}

function buildSortObject(queryParam: IQuery): any[] {
	const sort: any[] = [];

	if (queryParam.sortBy && queryParam.sortingOrder) {
		sort.push({
			[queryParam.sortBy]: queryParam.sortingOrder,
		});
	} else {
		sort.push(
			{
				createdAt: 'desc',
			},
			{
				id: 'asc',
			}
		);
	}

	return sort;
}

async function buildWhereClause(queryParam: IQuery): Promise<WhereClause> {
	let whereClauseObj: WhereClause = {};

	if (queryParam.fromDate && !moment(queryParam.fromDate, 'YYYY-MM-DD', true).isValid()) {
		throw new InvalidDateError('Invalid fromDate');
	}
	if (queryParam.toDate && !moment(queryParam.toDate, 'YYYY-MM-DD', true).isValid()) {
		throw new InvalidDateError('Invalid toDate');
	}

	if (hasText(queryParam.fromDate) || hasText(queryParam.toDate)) {
		whereClauseObj.createdAt = createDateRange(queryParam.fromDate, queryParam.toDate);
	}

	if (queryParam.searchFilters && Array.isArray(queryParam.searchFilters)) {
		queryParam.searchFilters.forEach((filter) => {
			if (hasText(filter.field) && hasText(filter.value)) {
				whereClauseObj[filter.field] = filter.value;
			}
		});
	}

	if (queryParam.searchFilter) {
		whereClauseObj.title = {
			contains: queryParam.searchFilter,
			mode: 'insensitive',
		};
	}

	return whereClauseObj;
}

async function getProducts(queryParam: IQuery) {
	let whereClauseObj = await buildWhereClause(queryParam);
	const sort = buildSortObject(queryParam);

	const products = await prisma.shopifyProducts.findMany({
		skip: getSkipCount(queryParam.pageNo, queryParam.pageSize),
		take: Number(queryParam.pageSize) || 10,
		orderBy: sort,
		where: whereClauseObj,
	});

	const _products = products.map((product) => {
		return {
			id: product.id,
			products: product?.title,
			salesPrice: product?.unitPrice,
			salesCOA: product?.salesCOA,
			purchasePrice: product?.purchasePrice,
			purchaseCOA: product?.purchaseCOA,
			tax: product?.tax,
		};
	});
	const count = await prisma.shopifyProducts.count({});

	return {
		content: _products,
		count,
	};
}

async function updateProducts(productData: any) {
	for (const product of productData) {
		const products = await prisma.shopifyProducts.update({
			where: { id: product.id },
			data: {
				unitPrice: product.salesPrice,
				salesCOA: product.salesCOA,
				purchasePrice: product.purchasePrice,
				purchaseCOA: product.purchaseCOA,
				tax: product.tax,
			},
		});
		await sendMessageToXeroProductQueue(products);
	}
	return {
		success: true,
	};
}

async function getXeroAccounts() {
	const xeroAccount = await prisma.xeroAccount.findMany({
		where: {
			status: 'ACTIVE',

			NOT: {
				code: null,
			},
			type: { notIn: ['INVENTORY'] },
		},
		select: { name: true, code: true },
		orderBy: {
			createdAt: 'desc',
		},
	});

	// const xeroAccountNames = xeroAccount.map((xeroAccount) => xeroAccount.name);
	const count = await prisma.xeroAccount.count({});

	return {
		content: xeroAccount,
		count,
	};
}

async function getTaxRates() {
	const connection: any = await prisma.connection.findFirst();

	if (!connection) {
		throw new ApiException(ErrorCodes.CONNECTION_NOT_FOUND);
	}

	const refreshToken = await xeroRefreshToken(connection);

	connection.xeroCredentials = refreshToken;

	const taxRateResponse = await axios.get('https://api.xero.com/api.xro/2.0/TaxRates', {
		headers: {
			Authorization: `Bearer ${connection.xeroCredentials.access_token}`,
			'Xero-Tenant-Id': connection.xeroCredentials.tenantId,
			Accept: 'application json',
		},
	});

	if (!taxRateResponse?.data?.TaxRates.length) {
		return null;
	}
	const activeTaxRates = taxRateResponse.data.TaxRates.filter((taxRate: any) => taxRate.Status === 'ACTIVE');
	const content = activeTaxRates.map((e: any) => {
		return {
			taxType: e?.Name,
		};
	});
	return {
		content: content,
	};
	// return taxRateResponse?.data?.TaxRates;
}

async function xeroRefreshToken(xeroData: any) {
	try {
		const clientId = process.env.XERO_CLIENT_ID;
		const clientSecret = process.env.XERO_CLIENT_SECRET;
		const refreshToken = xeroData.xeroCredentials.refresh_token;

		const authHeader = 'Basic ' + Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
		const params = new URLSearchParams();
		params.append('grant_type', 'refresh_token');
		params.append('refresh_token', refreshToken);

		const refreshTokenResponse = await axios.post('https://identity.xero.com/connect/token', params, {
			headers: {
				Authorization: authHeader,
				'Content-Type': 'application/x-www-form-urlencoded',
			},
		});

		const xeroTokenDetails = {
			...xeroData.xeroCredentials,
			access_token: refreshTokenResponse?.data?.access_token,
			refresh_token: refreshTokenResponse?.data?.refresh_token,
			id_token: refreshTokenResponse?.data?.id_token,
			expires_in: Math.floor(Date.now() / 1000) + 5 * 300,
		};

		await prisma.connection.update({
			where: {
				id: xeroData.id,
			},
			data: {
				xeroCredentials: xeroTokenDetails,
			},
		});

		return xeroTokenDetails;
	} catch (error: any) {
		console.error('Error refreshing token: ', error);
		throw error;
	}
}

async function getPaymentGateway(queryParam: IQuery) {
	let whereClauseObj = await buildWhereClause(queryParam);
	const sort = buildSortObject(queryParam);

	const paymentGateways = await prisma.paymentGateway.findMany({
		skip: getSkipCount(queryParam.pageNo, queryParam.pageSize),
		take: Number(queryParam.pageSize) || 10,
		orderBy: sort,
		where: whereClauseObj,
	});

	const _paymentGateways = paymentGateways.map((paymentGateway) => {
		return {
			id: paymentGateway.id,
			name: paymentGateway.name,
			coaAccount: paymentGateway?.coaAccount,
			coaCode: paymentGateway?.coaCode,
			feesAccount: paymentGateway?.feesAccount,
			feesCode: paymentGateway?.feesCode,
			date: paymentGateway?.createdAt,
		};
	});
	const count = await prisma.shopifyProducts.count({});

	return {
		content: _paymentGateways,
		count,
	};
}

async function updatePaymentGateway(paymentGatewayData: any) {
	for (const paymentGateway of paymentGatewayData) {
		const updatedPaymentGateways = await prisma.paymentGateway.update({
			where: { id: paymentGateway.id },
			data: {
				id: paymentGateway.id,
				name: paymentGateway.name,
				coaAccount: paymentGateway?.coaAccount,
				coaCode: paymentGateway?.coaCode,
				feesAccount: paymentGateway?.feesAccount,
				feesCode: paymentGateway?.feesCode,
				createdAt: paymentGateway?.date,
			},
		});
		// await sendMessageToXeroProductQueue(updatedPaymentGateways);
	}
	return {
		success: true,
	};
}

export const configurationService = {
	getProducts,
	getTaxRates,
	getXeroAccounts,
	xeroRefreshToken,
	updateProducts,
	getPaymentGateway,
	updatePaymentGateway,
};
