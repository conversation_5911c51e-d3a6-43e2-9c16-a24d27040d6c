import { prisma } from '../client/prisma';
import sendEmail from '../helpers/emailHelper';
import { comparePassword, hashPassword } from '../helpers/passwordHelper';
import {
	generateAccessToken,
	generateForgotPasswordToken,
	verifyForgotPasswordToken,
} from '../helpers/tokenHelper';
import {
	ForgotPassLinkRequest,
	LoginRequest,
	ResetPasswordRequest,
} from '../interfaces/authInterface';
import { ILoggedInUser, RequestExtended } from '../interfaces/global';
import { IUserProfileData } from '../interfaces/userInterface';
import { getForgotPasswordTemplate } from '../templates/email/forgotPasswordTemplate';
import ApiException from '../utils/errorHandler';
import { ErrorCodes } from '../utils/response';

async function login(data: LoginRequest) {
	const user = await prisma.users.findFirst({
		where: {
			email: data.email,
			isDeleted: false,
		},
		include: {
			role: true,
		},
	});

	if (!user) {
		throw new ApiException(ErrorCodes.INVALID_PASS_OR_EMAIL);
	}

	if (!(await comparePassword(data.password, user.password || ''))) {
		throw new ApiException(ErrorCodes.INVALID_CREDENTIALS);
	}

	if (!user.role.status) {
		throw new ApiException(ErrorCodes.ROLE_IS_NOT_ACTIVE);
	}

	if (!user.status) {
		throw new ApiException(ErrorCodes.USER_NOT_ACTIVE);
	}

	const token = generateAccessToken({
		id: user.id,
		name: user.name,
		type: user.role.type,
		roleId: user.roleId,
		email: user.email,
	});

	// await prisma.userToken.deleteMany({
	//     where: {
	//         userId: user.id
	//     }
	// });

	await prisma.userToken.create({
		data: {
			token,
			userId: user.id,
		},
	});

	return {
		token,
	};
}

async function generateForgotPassLink(data: ForgotPassLinkRequest) {
	const user = await prisma.users.findFirst({
		where: {
			email: data.email,
		},
	});

	if (!user) {
		throw new ApiException(ErrorCodes.USER_NOT_FOUND);
	}

	const forgotPassToken = generateForgotPasswordToken({
		email: data.email,
		userId: user.id,
	});

	await prisma.users.update({
		where: {
			id: user.id,
		},
		data: {
			forgotPasswordToken: forgotPassToken,
		},
	});

	const emailTemplate = getForgotPasswordTemplate({
		fullName: user.name,
		url: `${process.env.REACT_APP_BASE_URL}/reset-password?token=${forgotPassToken}`,
	});

	await sendEmail({
		from: `${process.env.SMTP_EMAIL}`,
		to: user.email,
		subject: 'Reset Password - Crystal Clear',
		html: emailTemplate,
	});

	return {
		success: true,
	};
}

async function resetPassword(data: ResetPasswordRequest) {
	const user = await prisma.users.findFirst({
		where: {
			forgotPasswordToken: data.token,
		},
	});

	if (!user) {
		throw new ApiException(ErrorCodes.INVALID_TOKEN);
	}

	try {
		const verifyToken = verifyForgotPasswordToken(data.token);

		if (!verifyToken) {
			throw new ApiException(ErrorCodes.INVALID_TOKEN);
		}
	} catch (error) {
		throw new ApiException(ErrorCodes.INVALID_TOKEN);
	}

	if (await comparePassword(data.password, user.password || '')) {
		throw new ApiException(ErrorCodes.SAME_PASSWORD);
	}

	await prisma.users.update({
		where: {
			id: user.id,
		},
		data: {
			forgotPasswordToken: null,
			password: await hashPassword(data.password),
		},
	});

	return {
		success: true,
	};
}

const logout = async (req: RequestExtended) => {
	const authHeader = req.headers.authorization || req.headers.Authorization;

	if (
		typeof authHeader !== 'string' ||
		(!authHeader?.startsWith('Bearer ') && !authHeader?.startsWith('bearer '))
	) {
		throw new ApiException(ErrorCodes.UNAUTHORIZED);
	}

	const token = authHeader.split(' ')[1];

	await prisma.userToken.deleteMany({
		where: {
			token: token,
		},
	});
	return {
		success: true,
	};
};

const fetchProfile = async (user: ILoggedInUser) => {
	const userData = await prisma.users.findFirst({
		where: { id: user.id },
		include: { role: true },
	});
	if (!userData) {
		throw new ApiException(ErrorCodes.USER_NOT_FOUND);
	}
	return {
		id: userData.id,
		email: userData.email,
		name: userData.name,
		roleId: userData.roleId,
		roleName: userData.role.name,
	};
};

const updateProfile = async (data: IUserProfileData, user: ILoggedInUser) => {
	const updatedUser = await prisma.users.update({
		where: { id: user.id },
		data: {
			name: data.name,
		},
		include: { role: true },
	});

	return {
		id: updatedUser.id,
		email: updatedUser.email,
		name: updatedUser.name,
		roleId: updatedUser.roleId,
		roleName: updatedUser.role.name,
	};
};

export const authService = {
	login,
	resetPassword,
	generateForgotPassLink,
	logout,
	fetchProfile,
	updateProfile,
};
