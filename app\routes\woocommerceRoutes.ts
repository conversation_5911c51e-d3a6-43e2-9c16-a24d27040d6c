import express, { Request, Response } from 'express';
import { invokeFunctionService } from '../services/invokeFunctionService';
import asyncHandler from '../utils/async-handler';

const router = express.Router();

router.get(
	'/salesOrders',
	asyncHandler(async (req: Request, res: Response) => {
		const result = await invokeFunctionService.invokeWoocommerceSalesOrder(req);
		res.status(200).json(result);
	})
);

export default router;
